{"name": "tango-admin", "version": "0.9.4", "private": true, "scripts": {"start": "node build/index.js", "dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "playwright test", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "proto:auth": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/auth node_modules/@tangopay/tango-cloud-grpc-proto/proto/auth.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:menu": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/menus node_modules/@tangopay/tango-cloud-grpc-proto/proto/menu-management.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:business": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/business/ node_modules/@tangopay/tango-cloud-grpc-proto/proto/business/*.proto --ts_proto_opt=fileSuffix=.pb", "proto:ordering": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/ordering/ node_modules/@tangopay/tango-cloud-grpc-proto/proto/ordering/*.proto --ts_proto_opt=fileSuffix=.pb", "proto:reporting": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/reporting/ node_modules/@tangopay/tango-cloud-grpc-proto/proto/reporting/*.proto --ts_proto_opt=fileSuffix=.pb", "proto:kds": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/kds node_modules/@tangopay/tango-cloud-grpc-proto/proto/kds.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:kdsV3": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/kdsV3 node_modules/@tangopay/tango-cloud-grpc-proto/proto/kds/kds.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:giftcards": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/giftcards/ node_modules/@tangopay/tango-cloud-grpc-proto/proto/gift-cards/*.proto --ts_proto_opt=fileSuffix=.pb", "proto:inventory": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/inventory/ node_modules/@tangopay/tango-cloud-grpc-proto/proto/inventory/*.proto --ts_proto_opt=fileSuffix=.pb", "proto:reputation": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/reputation node_modules/@tangopay/tango-cloud-grpc-proto/proto/reputation-management.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:qrcodes": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto --ts_proto_out=src/lib/calls/qrcodes node_modules/@tangopay/tango-cloud-grpc-proto/proto/qr-codes.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:people": "protoc  --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/people node_modules/@tangopay/tango-cloud-grpc-proto/proto/people/*.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:scheduling": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/scheduling node_modules/@tangopay/tango-cloud-grpc-proto/proto/scheduling/*.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:booking": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/booking node_modules/@tangopay/tango-cloud-grpc-proto/proto/booking/bookings.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:print": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/printing node_modules/@tangopay/tango-cloud-grpc-proto/proto/print/*.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto:banking": "protoc --proto_path=./node_modules/@tangopay/tango-cloud-grpc-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/@tangopay/tango-cloud-grpc-proto/proto --ts_proto_out=src/lib/calls/banking node_modules/@tangopay/tango-cloud-grpc-proto/proto/banking/*.proto --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=esModuleInterop=true", "proto": "yarn && yarn proto:auth && yarn proto:business && yarn proto:menu && yarn proto:ordering && yarn proto:reporting && yarn proto:giftcards && yarn proto:kds && yarn proto:inventory && yarn proto:reputation && yarn proto:qrcodes && yarn proto:people && yarn proto:scheduling && yarn proto:print && yarn proto:banking && yarn proto:kdsV3 && yarn proto:booking"}, "devDependencies": {"@playwright/test": "^1.36.0", "@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.15.2", "@types/chance": "^1.1.6", "@types/color": "^4.2.0", "@types/google.maps": "^3.53.4", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.14.195", "@types/markdown-it": "^12.2.3", "@types/numeral": "^2.0.2", "@types/pixi.js": "^5.0.0", "@types/plaid-link": "^2.0.15", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@types/validator": "^13.12.1", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "autoprefixer": "^10.4.14", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-svelte3": "^4.0.0", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.8.1", "svelte": "^3.54.0", "svelte-check": "^3.0.1", "tailwindcss": "^3.4.13", "ts-proto": "^1.137.0", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.2.0", "vitest": "^0.25.3"}, "type": "module", "dependencies": {"@airwallex/components-sdk": "^1.7.1", "@googlemaps/js-api-loader": "^1.16.2", "@intercom/messenger-js-sdk": "^0.0.12", "@sentry/sveltekit": "^7.57.0", "@sveltejs/adapter-node": "^1.2.3", "@tangopay/tango-cloud-grpc-proto": "2.6.22", "@tangopay/tango-svelte-library-v2": "2.1.21", "@tangopay/tango-unity": "1.1.12", "@types/animejs": "^3.1.12", "animejs": "^3.2.2", "chance": "^1.1.12", "chart.js": "^4.4.0", "chartjs-adapter-dayjs-4": "^1.0.4", "color": "^4.2.3", "dotenv": "^16.3.1", "gql-query-builder": "^3.8.0", "graphql": "^16.6.0", "graphql-request": "^6.0.0", "graphql-tag": "^2.12.6", "hash.js": "^1.1.7", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^3.0.0", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "numeral": "^2.0.6", "qrcode": "^1.5.3", "socket.io-client": "^4.7.5", "svelte-chartjs": "^3.1.2", "svelte-pixi": "^0.1.3", "uuid": "^9.0.1", "validator": "^13.12.0", "xlsx": "^0.18.5"}}