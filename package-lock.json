{"name": "tango-admin", "version": "0.9.4", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "tango-admin", "version": "0.9.4", "dependencies": {"@airwallex/components-sdk": "^1.7.1", "@googlemaps/js-api-loader": "^1.16.2", "@intercom/messenger-js-sdk": "^0.0.12", "@sentry/sveltekit": "^7.57.0", "@sveltejs/adapter-node": "^1.2.3", "@tangopay/tango-cloud-grpc-proto": "2.6.22", "@tangopay/tango-svelte-library-v2": "2.1.21", "@tangopay/tango-unity": "1.1.12", "@types/animejs": "^3.1.12", "animejs": "^3.2.2", "chance": "^1.1.12", "chart.js": "^4.4.0", "chartjs-adapter-dayjs-4": "^1.0.4", "color": "^4.2.3", "dotenv": "^16.3.1", "gql-query-builder": "^3.8.0", "graphql": "^16.6.0", "graphql-request": "^6.0.0", "graphql-tag": "^2.12.6", "hash.js": "^1.1.7", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^3.0.0", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "numeral": "^2.0.6", "pixi.js": "^8.10.2", "qrcode": "^1.5.3", "socket.io-client": "^4.7.5", "svelte-chartjs": "^3.1.2", "svelte-pixi": "^0.1.3", "uuid": "^9.0.1", "validator": "^13.12.0", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.36.0", "@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.15.2", "@types/chance": "^1.1.6", "@types/color": "^4.2.0", "@types/google.maps": "^3.53.4", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.14.195", "@types/markdown-it": "^12.2.3", "@types/numeral": "^2.0.2", "@types/pixi.js": "^5.0.0", "@types/plaid-link": "^2.0.15", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@types/validator": "^13.12.1", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "autoprefixer": "^10.4.14", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-svelte3": "^4.0.0", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.8.1", "svelte": "^3.54.0", "svelte-check": "^3.0.1", "tailwindcss": "^3.4.13", "ts-proto": "^1.137.0", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.2.0", "vitest": "^0.25.3"}}, "node_modules/@airwallex/airtracker": {"version": "1.2.0", "license": "ISC", "dependencies": {"web-vitals": "^3.5.1"}}, "node_modules/@airwallex/components-sdk": {"version": "1.7.1", "license": "MIT", "dependencies": {"@airwallex/airtracker": "1.2.0"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/parser": {"version": "7.22.7", "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.22.6", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.11"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/runtime-corejs3": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.28.0.tgz", "integrity": "sha512-nlIXnSqLcBij8K8TtkxbBJgfzfvi75V1pAKSM7dUXejGw12vJAqez74jZrHTsJ3Z+Aczc5Q/6JgNjKRMsVU44g==", "dependencies": {"core-js-pure": "^3.43.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.17.14", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.5.0", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.5.1", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "8.37.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@googlemaps/js-api-loader": {"version": "1.16.2", "license": "Apache-2.0", "dependencies": {"fast-deep-equal": "^3.1.3"}}, "node_modules/@graphql-typed-document-node/core": {"version": "3.2.0", "license": "MIT", "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.8", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@imask/svelte": {"version": "7.6.1", "resolved": "https://registry.npmjs.org/@imask/svelte/-/svelte-7.6.1.tgz", "integrity": "sha512-NVGhgV+endyrmG8srUmNjbULmZs8Wq9B7YFzV/jKtC3EYWhMzccgvQ27IEkRFQ9dcxaPjmwni9UejfrkK1oeow==", "dependencies": {"imask": "^7.6.1"}, "peerDependencies": {"svelte": "^4.2.12"}}, "node_modules/@intercom/messenger-js-sdk": {"version": "0.0.12", "license": "MIT"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/gen-mapping/node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/gen-mapping/node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/gen-mapping/node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@jridgewell/gen-mapping/node_modules/@jridgewell/trace-mapping/node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "license": "MIT"}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.17", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "node_modules/@kurkle/color": {"version": "0.3.2", "license": "MIT"}, "node_modules/@mixmark-io/domino": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@mixmark-io/domino/-/domino-2.2.0.tgz", "integrity": "sha512-Y28PR25bHXUg88kCV7nivXrP2Nj2RueZ3/l/jdx6J9f8J4nsEGcgX0Qe6lt7Pa+J79+kPiJU3LguR6O/6zrLOw=="}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pixi/colord": {"version": "2.9.6", "resolved": "https://registry.npmjs.org/@pixi/colord/-/colord-2.9.6.tgz", "integrity": "sha512-nezytU2pw587fQstUu1AsJZDVEynjskwOL+kibwcdxsMBFqPsFFNA7xl0ii/gXuDi6M0xj3mfRJj8pBSc2jCfA=="}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@playwright/test": {"version": "1.36.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/node": "*", "playwright-core": "1.36.0"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=16"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/@polka/url": {"version": "1.0.0-next.21", "license": "MIT"}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/path": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@rollup/plugin-commonjs": {"version": "24.0.1", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^5.0.1", "commondir": "^1.0.1", "estree-walker": "^2.0.2", "glob": "^8.0.3", "is-reference": "1.2.1", "magic-string": "^0.27.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^2.68.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/plugin-commonjs/node_modules/glob": {"version": "8.1.0", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@rollup/plugin-commonjs/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/@rollup/plugin-json": {"version": "6.0.0", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^5.0.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/plugin-node-resolve": {"version": "15.0.1", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^5.0.1", "@types/resolve": "1.20.2", "deepmerge": "^4.2.2", "is-builtin-module": "^3.2.0", "is-module": "^1.0.0", "resolve": "^1.22.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^2.78.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/pluginutils": {"version": "5.0.2", "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@sentry-internal/tracing": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/core": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry-internal/tracing/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/browser": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry-internal/tracing": "7.57.0", "@sentry/core": "7.57.0", "@sentry/replay": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/browser/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/bundler-plugin-core": {"version": "0.6.0", "license": "MIT", "dependencies": {"@sentry/cli": "^2.17.0", "@sentry/node": "^7.19.0", "@sentry/tracing": "^7.19.0", "find-up": "5.0.0", "glob": "9.3.2", "magic-string": "0.27.0", "unplugin": "1.0.1", "webpack-sources": "3.2.3"}, "engines": {"node": ">= 10"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/glob": {"version": "9.3.2", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "minimatch": "^7.4.1", "minipass": "^4.2.4", "path-scurry": "^1.6.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/lru-cache": {"version": "10.0.0", "license": "ISC", "engines": {"node": "14 || >=16.14"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/minimatch": {"version": "7.4.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/minipass": {"version": "4.2.8", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/path-scurry": {"version": "1.10.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^9.1.1 || ^10.0.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@sentry/bundler-plugin-core/node_modules/path-scurry/node_modules/minipass": {"version": "7.0.1", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/@sentry/cli": {"version": "2.19.4", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"https-proxy-agent": "^5.0.0", "node-fetch": "^2.6.7", "progress": "^2.0.3", "proxy-from-env": "^1.1.0", "which": "^2.0.2"}, "bin": {"sentry-cli": "bin/sentry-cli"}, "engines": {"node": ">= 10"}}, "node_modules/@sentry/core": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/core/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/integrations": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "localforage": "^1.8.1", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/integrations/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/node": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry-internal/tracing": "7.57.0", "@sentry/core": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "cookie": "^0.4.1", "https-proxy-agent": "^5.0.0", "lru_map": "^0.3.3", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/node/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/replay": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/core": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0"}, "engines": {"node": ">=12"}}, "node_modules/@sentry/svelte": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/browser": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "magic-string": "^0.30.0", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}, "peerDependencies": {"svelte": "3.x || 4.x"}}, "node_modules/@sentry/svelte/node_modules/magic-string": {"version": "0.30.0", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "engines": {"node": ">=12"}}, "node_modules/@sentry/svelte/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/sveltekit": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry-internal/tracing": "7.57.0", "@sentry/core": "7.57.0", "@sentry/integrations": "7.57.0", "@sentry/node": "7.57.0", "@sentry/svelte": "7.57.0", "@sentry/types": "7.57.0", "@sentry/utils": "7.57.0", "@sentry/vite-plugin": "^0.6.0", "magicast": "0.2.8", "sorcery": "0.11.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@sveltejs/kit": "1.x"}}, "node_modules/@sentry/tracing": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry-internal/tracing": "7.57.0"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/types": {"version": "7.57.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@sentry/utils": {"version": "7.57.0", "license": "MIT", "dependencies": {"@sentry/types": "7.57.0", "tslib": "^2.4.1 || ^1.9.3"}, "engines": {"node": ">=8"}}, "node_modules/@sentry/utils/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/@sentry/vite-plugin": {"version": "0.6.0", "license": "MIT", "dependencies": {"@sentry/bundler-plugin-core": "0.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/@socket.io/component-emitter": {"version": "3.1.2", "license": "MIT"}, "node_modules/@svelte-put/clickoutside": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@svelte-put/clickoutside/-/clickoutside-2.0.0.tgz", "integrity": "sha512-04xYxOT5Ti+VlP70+uY/a5tGnVWl3XFgnRiO9bMSHbkwOlzHxSgXyM1NCS4NV09B3i41LRuShgeMrbgU0J5cPg=="}, "node_modules/@sveltejs/adapter-auto": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"import-meta-resolve": "^2.2.0"}, "peerDependencies": {"@sveltejs/kit": "^1.0.0"}}, "node_modules/@sveltejs/adapter-node": {"version": "1.2.3", "license": "MIT", "dependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "rollup": "^3.7.0"}, "peerDependencies": {"@sveltejs/kit": "^1.0.0"}}, "node_modules/@sveltejs/kit": {"version": "1.22.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"@sveltejs/vite-plugin-svelte": "^2.4.1", "@types/cookie": "^0.5.1", "cookie": "^0.5.0", "devalue": "^4.3.1", "esm-env": "^1.0.0", "kleur": "^4.1.5", "magic-string": "^0.30.0", "mime": "^3.0.0", "sade": "^1.8.1", "set-cookie-parser": "^2.6.0", "sirv": "^2.0.2", "undici": "~5.22.0"}, "bin": {"svelte-kit": "svelte-kit.js"}, "engines": {"node": "^16.14 || >=18"}, "peerDependencies": {"svelte": "^3.54.0 || ^4.0.0-next.0", "vite": "^4.0.0"}}, "node_modules/@sveltejs/kit/node_modules/cookie": {"version": "0.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@sveltejs/kit/node_modules/magic-string": {"version": "0.30.0", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "engines": {"node": ">=12"}}, "node_modules/@sveltejs/vite-plugin-svelte": {"version": "2.4.3", "license": "MIT", "dependencies": {"@sveltejs/vite-plugin-svelte-inspector": "^1.0.3", "debug": "^4.3.4", "deepmerge": "^4.3.1", "kleur": "^4.1.5", "magic-string": "^0.30.1", "svelte-hmr": "^0.15.2", "vitefu": "^0.2.4"}, "engines": {"node": "^14.18.0 || >= 16"}, "peerDependencies": {"svelte": "^3.54.0 || ^4.0.0", "vite": "^4.0.0"}}, "node_modules/@sveltejs/vite-plugin-svelte-inspector": {"version": "1.0.3", "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": "^14.18.0 || >= 16"}, "peerDependencies": {"@sveltejs/vite-plugin-svelte": "^2.2.0", "svelte": "^3.54.0 || ^4.0.0", "vite": "^4.0.0"}}, "node_modules/@sveltejs/vite-plugin-svelte/node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "license": "MIT"}, "node_modules/@sveltejs/vite-plugin-svelte/node_modules/magic-string": {"version": "0.30.2", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "engines": {"node": ">=12"}}, "node_modules/@tangopay/tango-cloud-grpc-proto": {"version": "2.6.22", "resolved": "https://npm.pkg.github.com/download/@tangopay/tango-cloud-grpc-proto/2.6.22/5ee60f6dec7a7514b643e8ef1ac0d90178b96148", "integrity": "sha512-ZWFXjJ4dbiCXztpnvhWJfEJ4cksacdrS40wlUhdAT89jIHmhy5IraGUQDu54Y5bXwxn28Ef6tLqnDoKKllzT0g=="}, "node_modules/@tangopay/tango-svelte-library-v2": {"version": "2.1.21", "resolved": "https://npm.pkg.github.com/download/@tangopay/tango-svelte-library-v2/2.1.21/ef799daeb1663811c345d8d54a34d74efe83c9f1", "integrity": "sha512-cwUHKRJxiXDuo30RAli5I1Agyo4HHCpBcDpYOCl6QViHLBl/PbNbn6B6mfIJwYq55ofmyxxCTcAXyXegoS079Q==", "dependencies": {"@imask/svelte": "^7.1.3", "@svelte-put/clickoutside": "^2.0.0", "csv42": "^3.0.3", "dayjs": "^1.11.7", "emoji-picker-element": "^1.18.3", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "numeral": "^2.0.6", "quill": "1.3.6", "svelte-imask": "^1.2.0", "tailwind-scrollbar-hide": "^1.1.7", "turndown": "^7.1.2", "uuid": "^9.0.1", "validator": "^13.9.0"}, "peerDependencies": {"svelte": "^3.54.0"}}, "node_modules/@tangopay/tango-unity": {"version": "1.1.12", "resolved": "https://npm.pkg.github.com/download/@tangopay/tango-unity/1.1.12/9a974f95cc3460582f3dc13d007b64ad62fb791f", "integrity": "sha512-1fIyIqalKf8kPjv8ULTjmGjXjj5EfQLCBUQWJBS/lOG/MnO2onr80a30W3hFBM2f8gAdoxptgjryXua98F3+0w==", "dependencies": {"dayjs": "^1.11.8", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.29", "lodash": "^4.17.21", "numeral": "^2.0.6", "validator": "^13.9.0"}}, "node_modules/@types/animejs": {"version": "3.1.12", "license": "MIT"}, "node_modules/@types/chai": {"version": "4.3.4", "dev": true, "license": "MIT"}, "node_modules/@types/chai-subset": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "*"}}, "node_modules/@types/chance": {"version": "1.1.6", "dev": true, "license": "MIT"}, "node_modules/@types/color": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/color-convert": "*"}}, "node_modules/@types/color-convert": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/color-name": "^1.1.0"}}, "node_modules/@types/color-name": {"version": "1.1.5", "dev": true, "license": "MIT"}, "node_modules/@types/cookie": {"version": "0.5.1", "license": "MIT"}, "node_modules/@types/css-font-loading-module": {"version": "0.0.12", "resolved": "https://registry.npmjs.org/@types/css-font-loading-module/-/css-font-loading-module-0.0.12.tgz", "integrity": "sha512-x2tZZYkSxXqWvTDgveSynfjq/T2HyiZHXb00j/+gy19yp70PHCizM48XFdjBCWH7eHBD0R5i/pw9yMBP/BH5uA=="}, "node_modules/@types/earcut": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@types/earcut/-/earcut-3.0.0.tgz", "integrity": "sha512-k/9fOUGO39yd2sCjrbAJvGDEQvRwRnQIZlBz43roGwUZo5SHAmyVvSFyaVVZkicRVCaDXPKlbxrUcBuJoSWunQ=="}, "node_modules/@types/estree": {"version": "1.0.0", "license": "MIT"}, "node_modules/@types/google.maps": {"version": "3.53.4", "dev": true, "license": "MIT"}, "node_modules/@types/js-cookie": {"version": "3.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.11", "dev": true, "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "dev": true, "license": "MIT"}, "node_modules/@types/linkify-it": {"version": "3.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.14.195", "dev": true, "license": "MIT"}, "node_modules/@types/long": {"version": "4.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/markdown-it": {"version": "12.2.3", "dev": true, "license": "MIT", "dependencies": {"@types/linkify-it": "*", "@types/mdurl": "*"}}, "node_modules/@types/mdurl": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "18.15.11", "devOptional": true, "license": "MIT"}, "node_modules/@types/numeral": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/object-hash": {"version": "1.3.4", "dev": true, "license": "MIT"}, "node_modules/@types/pixi.js": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"pixi.js": "*"}}, "node_modules/@types/plaid-link": {"version": "2.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/pug": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/qrcode": {"version": "1.5.5", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/raf": {"version": "3.4.0", "license": "MIT", "optional": true}, "node_modules/@types/resolve": {"version": "1.20.2", "license": "MIT"}, "node_modules/@types/semver": {"version": "7.3.13", "dev": true, "license": "MIT"}, "node_modules/@types/uuid": {"version": "9.0.7", "dev": true, "license": "MIT"}, "node_modules/@types/validator": {"version": "13.12.1", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.57.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.57.0", "@typescript-eslint/type-utils": "5.57.0", "@typescript-eslint/utils": "5.57.0", "debug": "^4.3.4", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "5.57.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.57.0", "@typescript-eslint/types": "5.57.0", "@typescript-eslint/typescript-estree": "5.57.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.57.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.57.0", "@typescript-eslint/visitor-keys": "5.57.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.57.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.57.0", "@typescript-eslint/utils": "5.57.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.57.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.57.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.57.0", "@typescript-eslint/visitor-keys": "5.57.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "5.57.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.57.0", "@typescript-eslint/types": "5.57.0", "@typescript-eslint/typescript-estree": "5.57.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.57.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.57.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@webgpu/types": {"version": "0.1.63", "resolved": "https://registry.npmjs.org/@webgpu/types/-/types-0.1.63.tgz", "integrity": "sha512-s9Kuh0nE/2+nKrvmKNMB2fE5Zlr3DL2t3OFKM55v5jRcfCOxbkOHhQoshoFum5mmXIfEtRXtLCWmkeTJsVjE9w=="}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "resolved": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "integrity": "sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==", "engines": {"node": ">=10.0.0"}}, "node_modules/acorn": {"version": "8.8.2", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/adler-32": {"version": "1.3.1", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/animejs": {"version": "3.2.2", "license": "MIT"}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/array-buffer-byte-length": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array.prototype.flat": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/assert": {"version": "2.0.0", "license": "MIT", "dependencies": {"es6-object-assign": "^1.1.0", "is-nan": "^1.2.1", "object-is": "^1.0.1", "util": "^0.12.0"}}, "node_modules/assertion-error": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/ast-types": {"version": "0.16.1", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/ast-types/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/autoprefixer": {"version": "10.4.14", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}], "license": "MIT", "dependencies": {"browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001464", "fraction.js": "^4.2.0", "normalize-range": "^0.1.2", "picocolors": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base64-arraybuffer": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/binary-extensions": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.2", "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.21.5", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001449", "electron-to-chromium": "^1.4.284", "node-releases": "^2.0.8", "update-browserslist-db": "^1.0.10"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/btoa": {"version": "1.2.1", "license": "(MIT OR Apache-2.0)", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "license": "MIT", "engines": {"node": "*"}}, "node_modules/builtin-modules": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/busboy": {"version": "1.6.0", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dev": true, "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase-css": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001473", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/canvg": {"version": "3.0.10", "license": "MIT", "optional": true, "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/case-anything": {"version": "2.1.10", "dev": true, "license": "MIT", "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/cfb": {"version": "1.2.2", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "engines": {"node": ">=0.8"}}, "node_modules/chai": {"version": "4.3.7", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^4.1.2", "get-func-name": "^2.0.0", "loupe": "^2.3.1", "pathval": "^1.1.1", "type-detect": "^4.0.5"}, "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chance": {"version": "1.1.12", "license": "MIT"}, "node_modules/chart.js": {"version": "4.4.0", "license": "MIT", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=7"}}, "node_modules/chartjs-adapter-dayjs-4": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"chart.js": ">=4.0.1", "dayjs": "^1.9.7"}}, "node_modules/check-error": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/chokidar": {"version": "3.5.3", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/cliui": {"version": "6.0.0", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "engines": {"node": ">=0.8"}}, "node_modules/codepage": {"version": "1.15.0", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/color": {"version": "4.2.3", "license": "MIT", "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/commondir": {"version": "1.0.1", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/cookie": {"version": "0.4.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/core-js": {"version": "3.31.1", "hasInstallScript": true, "license": "MIT", "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-pure": {"version": "3.43.0", "resolved": "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.43.0.tgz", "integrity": "sha512-i/AgxU2+A+BbJdMxh3v7/vxi2SbFqxiFmg6VsDwYB4jkucrd1BZNA9a9gphC0fYMG5IBSgQcbQnk865VCLe7xA==", "hasInstallScript": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/crc-32": {"version": "1.2.2", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/cross-fetch": {"version": "3.1.5", "license": "MIT", "dependencies": {"node-fetch": "2.6.7"}}, "node_modules/cross-fetch/node_modules/node-fetch": {"version": "2.6.7", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-line-break": {"version": "2.1.0", "license": "MIT", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csv42": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/csv42/-/csv42-3.0.4.tgz", "integrity": "sha512-tLjSa9EiI5Gc/T/ux15wU6NU0Di+XqTd2P1CLHsSRr4jykpeLoPs9vPINNGCWzZEiSAAUv+i2q4pE/GOKnUXIQ=="}, "node_modules/dataloader": {"version": "1.4.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/deep-eql": {"version": "4.1.3", "dev": true, "license": "MIT", "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deep-equal": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz", "integrity": "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==", "dependencies": {"is-arguments": "^1.1.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "object-is": "^1.1.5", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.5.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "4.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/detect-indent": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/detect-libc": {"version": "1.0.3", "dev": true, "license": "Apache-2.0", "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/devalue": {"version": "4.3.2", "license": "MIT"}, "node_modules/didyoumean": {"version": "1.2.2", "license": "Apache-2.0"}, "node_modules/dijkstrajs": {"version": "1.0.3", "license": "MIT"}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dlv": {"version": "1.1.3", "license": "MIT"}, "node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dompurify": {"version": "2.4.6", "license": "(MPL-2.0 OR Apache-2.0)", "optional": true}, "node_modules/dotenv": {"version": "16.3.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/motdotla/dotenv?sponsor=1"}}, "node_modules/dprint-node": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"detect-libc": "^1.0.3"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/earcut": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/earcut/-/earcut-3.0.1.tgz", "integrity": "sha512-0l1/0gOjESMeQyYaK5IDiPNvFeu93Z/cO0TjZh9eZ1vyCtZnA7KMZ8rQggpsJHIbGSdrqYq9OhuveadOVHCshw=="}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.4.347", "dev": true, "license": "ISC"}, "node_modules/emoji-picker-element": {"version": "1.26.3", "resolved": "https://registry.npmjs.org/emoji-picker-element/-/emoji-picker-element-1.26.3.tgz", "integrity": "sha512-fOMG44d/3OqTe1pPqlu5H4ZtWg7gK4Le6Bt24JTKtDyce5+EO3Mo8WA95cKHbPSsSsg7ehM12M1x3Y6U6fgvTQ=="}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encode-utf8": {"version": "1.0.3", "license": "MIT"}, "node_modules/encoding": {"version": "0.1.13", "license": "MIT", "optional": true, "peer": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/engine.io-client": {"version": "6.5.4", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1", "xmlhttprequest-ssl": "~2.0.0"}}, "node_modules/engine.io-client/node_modules/debug": {"version": "4.3.6", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/engine.io-parser": {"version": "5.2.3", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/entities": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/es-abstract": {"version": "1.21.2", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.0", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es6-object-assign": {"version": "1.1.0", "license": "MIT"}, "node_modules/es6-promise": {"version": "3.3.1", "license": "MIT"}, "node_modules/esbuild": {"version": "0.17.14", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.17.14", "@esbuild/android-arm64": "0.17.14", "@esbuild/android-x64": "0.17.14", "@esbuild/darwin-arm64": "0.17.14", "@esbuild/darwin-x64": "0.17.14", "@esbuild/freebsd-arm64": "0.17.14", "@esbuild/freebsd-x64": "0.17.14", "@esbuild/linux-arm": "0.17.14", "@esbuild/linux-arm64": "0.17.14", "@esbuild/linux-ia32": "0.17.14", "@esbuild/linux-loong64": "0.17.14", "@esbuild/linux-mips64el": "0.17.14", "@esbuild/linux-ppc64": "0.17.14", "@esbuild/linux-riscv64": "0.17.14", "@esbuild/linux-s390x": "0.17.14", "@esbuild/linux-x64": "0.17.14", "@esbuild/netbsd-x64": "0.17.14", "@esbuild/openbsd-x64": "0.17.14", "@esbuild/sunos-x64": "0.17.14", "@esbuild/win32-arm64": "0.17.14", "@esbuild/win32-ia32": "0.17.14", "@esbuild/win32-x64": "0.17.14"}}, "node_modules/escalade": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.37.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.0.2", "@eslint/js": "8.37.0", "@humanwhocodes/config-array": "^0.11.8", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.4.0", "espree": "^9.5.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-sdsl": "^4.1.4", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "8.8.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.7", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.11.0", "resolve": "^1.22.1"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-import-resolver-node/node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "node_modules/eslint-module-utils": {"version": "2.8.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils/node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "node_modules/eslint-plugin-import": {"version": "2.27.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "array.prototype.flatmap": "^1.3.1", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.7", "eslint-module-utils": "^2.7.4", "has": "^1.0.3", "is-core-module": "^2.11.0", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.values": "^1.1.6", "resolve": "^1.22.1", "semver": "^6.3.0", "tsconfig-paths": "^3.14.1"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "node_modules/eslint-plugin-import/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-plugin-svelte3": {"version": "4.0.0", "dev": true, "license": "MIT", "peerDependencies": {"eslint": ">=8.0.0", "svelte": "^3.2.0"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/eslint/node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/eslint/node_modules/eslint-scope": {"version": "7.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/eslint/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/esm-env": {"version": "1.0.0", "license": "MIT"}, "node_modules/espree": {"version": "9.5.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA=="}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-diff": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz", "integrity": "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig=="}, "node_modules/fast-glob": {"version": "3.2.12", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.15.0", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fflate": {"version": "0.4.8", "license": "MIT"}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.0.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flat-cache/node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/flat-cache/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/flatted": {"version": "3.2.7", "dev": true, "license": "ISC"}, "node_modules/for-each": {"version": "0.3.3", "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/foreground-child": {"version": "3.3.0", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/frac": {"version": "1.1.2", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/fraction.js": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://www.patreon.com/infusion"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.2", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-func-name": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gifuct-js": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/gifuct-js/-/gifuct-js-2.1.2.tgz", "integrity": "sha512-rI2asw77u0mGgwhV3qA+OEgYqaDn5UNqgs+Bx0FGwSpuqfYn+Ir6RQY5ENNQ8SbIiG/m5gVa7CD5RriO4f4Lsg==", "dependencies": {"js-binary-schema-parser": "^2.0.3"}}, "node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "13.20.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gql-query-builder": {"version": "3.8.0", "license": "MIT"}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/grapheme-splitter": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/graphql": {"version": "16.6.0", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/graphql-request": {"version": "6.0.0", "license": "MIT", "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "cross-fetch": "^3.1.5"}, "peerDependencies": {"graphql": "14 - 16"}}, "node_modules/graphql-tag": {"version": "2.12.6", "license": "MIT", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/has": {"version": "1.0.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/html2canvas": {"version": "1.4.1", "license": "MIT", "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}, "engines": {"node": ">=8.0.0"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent/node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "optional": true, "peer": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/imask": {"version": "7.6.1", "resolved": "https://registry.npmjs.org/imask/-/imask-7.6.1.tgz", "integrity": "sha512-sJlIFM7eathUEMChTh9Mrfw/IgiWgJqBKq2VNbyXvBZ7ev/IlO6/KQTKlV/Fm+viQMLrFLG/zCuudrLIwgK2dg==", "dependencies": {"@babel/runtime-corejs3": "^7.24.4"}, "engines": {"npm": ">=4.0.0"}}, "node_modules/immediate": {"version": "3.0.6", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-meta-resolve": {"version": "2.2.2", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/internal-slot": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-arguments": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.3.2", "license": "MIT"}, "node_modules/is-bigint": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-builtin-module": {"version": "3.2.1", "license": "MIT", "dependencies": {"builtin-modules": "^3.3.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.12.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.0.10", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-module": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-nan": {"version": "1.3.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-reference": {"version": "1.2.1", "license": "MIT", "dependencies": {"@types/estree": "*"}}, "node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-string": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.10", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/ismobilejs": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ismobilejs/-/ismobilejs-1.1.1.tgz", "integrity": "sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw=="}, "node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jiti": {"version": "1.21.6", "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/js-binary-schema-parser": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/js-binary-schema-parser/-/js-binary-schema-parser-2.0.3.tgz", "integrity": "sha512-xezGJmOb4lk/M1ZZLTR/jaBHQ4gG/lqQnJqdIv4721DMggsa1bDVlHXNeHYogaIEHD9vCRv0fcL4hMA+Coarkg=="}, "node_modules/js-cookie": {"version": "3.0.5", "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/js-sdsl": {"version": "4.4.0", "dev": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/jspdf": {"version": "2.5.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.14.0", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.4.8"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.2.0", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/jspdf-autotable": {"version": "3.8.2", "license": "MIT", "peerDependencies": {"jspdf": "^2.5.1"}}, "node_modules/jwt-decode": {"version": "3.1.2", "license": "MIT"}, "node_modules/kleur": {"version": "4.1.5", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lie": {"version": "3.1.1", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/lilconfig": {"version": "3.1.3", "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/linkify-it": {"version": "4.0.1", "license": "MIT", "dependencies": {"uc.micro": "^1.0.1"}}, "node_modules/local-pkg": {"version": "0.4.3", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/localforage": {"version": "1.10.0", "license": "Apache-2.0", "dependencies": {"lie": "3.1.1"}}, "node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/long": {"version": "4.0.0", "dev": true, "license": "Apache-2.0"}, "node_modules/loupe": {"version": "2.3.6", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.0"}}, "node_modules/lru_map": {"version": "0.3.3", "license": "MIT"}, "node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/magic-string": {"version": "0.27.0", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "engines": {"node": ">=12"}}, "node_modules/magicast": {"version": "0.2.8", "license": "MIT", "dependencies": {"@babel/parser": "^7.21.9", "@babel/types": "^7.21.5", "recast": "^0.23.2"}}, "node_modules/magicast/node_modules/@babel/helper-string-parser": {"version": "7.22.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/magicast/node_modules/@babel/helper-validator-identifier": {"version": "7.22.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/magicast/node_modules/@babel/types": {"version": "7.22.5", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/markdown-it": {"version": "13.0.1", "license": "MIT", "dependencies": {"argparse": "^2.0.1", "entities": "~3.0.1", "linkify-it": "^4.0.1", "mdurl": "^1.0.1", "uc.micro": "^1.0.5"}, "bin": {"markdown-it": "bin/markdown-it.js"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "engines": {"node": ">= 0.4"}}, "node_modules/mdurl": {"version": "1.0.1", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "3.0.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/min-indent": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mri": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/mrmime": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/node-fetch": {"version": "2.6.12", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-releases": {"version": "2.0.10", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/numeral": {"version": "2.0.6", "license": "MIT", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.values": {"version": "1.1.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/optionator": {"version": "0.9.1", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/parchment": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz", "integrity": "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg=="}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-svg-path": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz", "integrity": "sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ=="}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/minipass": {"version": "7.0.1", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathval": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT", "optional": true}, "node_modules/picocolors": {"version": "1.0.0", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.5", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pixi.js": {"version": "8.10.2", "resolved": "https://registry.npmjs.org/pixi.js/-/pixi.js-8.10.2.tgz", "integrity": "sha512-utRKxzTwNsIhaxOikxIBxKPfxuOyVkPvdYipY23ZEyWSYhONosrQlcB9nymeIcbsrsuSdwExX0eTnHTDjTN3UQ==", "dependencies": {"@pixi/colord": "^2.9.6", "@types/css-font-loading-module": "^0.0.12", "@types/earcut": "^3.0.0", "@webgpu/types": "^0.1.40", "@xmldom/xmldom": "^0.8.10", "earcut": "^3.0.1", "eventemitter3": "^5.0.1", "gifuct-js": "^2.1.2", "ismobilejs": "^1.1.1", "parse-svg-path": "^0.1.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "node_modules/playwright-core": {"version": "1.36.0", "dev": true, "license": "Apache-2.0", "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=16"}}, "node_modules/pngjs": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/postcss": {"version": "8.4.21", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.7", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-plugin-svelte": {"version": "2.10.0", "dev": true, "license": "MIT", "peerDependencies": {"prettier": "^1.16.4 || ^2.0.0", "svelte": "^3.2.0"}}, "node_modules/progress": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/protobufjs": {"version": "6.11.3", "dev": true, "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qrcode": {"version": "1.5.3", "license": "MIT", "dependencies": {"dijkstrajs": "^1.0.1", "encode-utf8": "^1.0.3", "pngjs": "^5.0.0", "yargs": "^15.3.1"}, "bin": {"qrcode": "bin/qrcode"}, "engines": {"node": ">=10.13.0"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quill": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/quill/-/quill-1.3.6.tgz", "integrity": "sha512-K0mvhimWZN6s+9OQ249CH2IEPZ9JmkFuCQeHAOQax3EZ2nDJ3wfGh59mnlQaZV2i7u8eFarx6wAtvQKgShojug==", "dependencies": {"clone": "^2.1.1", "deep-equal": "^1.0.1", "eventemitter3": "^2.0.3", "extend": "^3.0.1", "parchment": "^1.1.4", "quill-delta": "^3.6.2"}}, "node_modules/quill-delta": {"version": "3.6.3", "resolved": "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz", "integrity": "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==", "dependencies": {"deep-equal": "^1.0.1", "extend": "^3.0.2", "fast-diff": "1.1.2"}, "engines": {"node": ">=0.10"}}, "node_modules/quill/node_modules/eventemitter3": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz", "integrity": "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg=="}, "node_modules/raf": {"version": "3.4.1", "license": "MIT", "optional": true, "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/read-cache": {"version": "1.0.0", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/recast": {"version": "0.23.2", "license": "MIT", "dependencies": {"assert": "^2.0.0", "ast-types": "^0.16.1", "esprima": "~4.0.0", "source-map": "~0.6.1", "tslib": "^2.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/recast/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "2.0.0", "license": "ISC"}, "node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve/node_modules/is-core-module": {"version": "2.11.0", "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rgbcolor": {"version": "1.0.1", "license": "MIT OR SEE LICENSE IN FEEL-FREE.md", "optional": true, "engines": {"node": ">= 0.8.15"}}, "node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rollup": {"version": "3.20.2", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/sade": {"version": "1.8.1", "license": "MIT", "dependencies": {"mri": "^1.1.0"}, "engines": {"node": ">=6"}}, "node_modules/safe-regex-test": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT", "optional": true, "peer": true}, "node_modules/sander": {"version": "0.5.1", "license": "MIT", "dependencies": {"es6-promise": "^3.1.2", "graceful-fs": "^4.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.2"}}, "node_modules/sander/node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/semver": {"version": "7.3.8", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "node_modules/set-cookie-parser": {"version": "2.6.0", "license": "MIT"}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz", "integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dev": true, "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dev": true, "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dev": true, "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dev": true, "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/sirv": {"version": "2.0.2", "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.20", "mrmime": "^1.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/socket.io-client": {"version": "4.7.5", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.2", "engine.io-client": "~6.5.2", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-client/node_modules/debug": {"version": "4.3.6", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socket.io-parser": {"version": "4.2.4", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-parser/node_modules/debug": {"version": "4.3.6", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/sorcery": {"version": "0.11.0", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.14", "buffer-crc32": "^0.2.5", "minimist": "^1.2.0", "sander": "^0.5.0"}, "bin": {"sorcery": "bin/sorcery"}}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/ssf": {"version": "0.11.2", "license": "Apache-2.0", "dependencies": {"frac": "~1.1.2"}, "engines": {"node": ">=0.8"}}, "node_modules/stackblur-canvas": {"version": "2.6.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.14"}}, "node_modules/streamsearch": {"version": "1.1.0", "engines": {"node": ">=10.0.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.trim": {"version": "1.2.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-indent": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-literal": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.8.2"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/sucrase": {"version": "3.35.0", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svelte": {"version": "3.58.0", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/svelte-chartjs": {"version": "3.1.2", "license": "MIT", "peerDependencies": {"chart.js": "^3.5.0 || ^4.0.0", "svelte": "^3.45.0"}}, "node_modules/svelte-check": {"version": "3.1.4", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.17", "chokidar": "^3.4.1", "fast-glob": "^3.2.7", "import-fresh": "^3.2.1", "picocolors": "^1.0.0", "sade": "^1.7.4", "svelte-preprocess": "^5.0.0", "typescript": "^4.9.4"}, "bin": {"svelte-check": "bin/svelte-check"}, "peerDependencies": {"svelte": "^3.55.0"}}, "node_modules/svelte-check/node_modules/svelte-preprocess": {"version": "5.0.3", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@types/pug": "^2.0.6", "detect-indent": "^6.1.0", "magic-string": "^0.27.0", "sorcery": "^0.11.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">= 14.10.0"}, "peerDependencies": {"@babel/core": "^7.10.2", "coffeescript": "^2.5.1", "less": "^3.11.3 || ^4.0.0", "postcss": "^7 || ^8", "postcss-load-config": "^2.1.0 || ^3.0.0 || ^4.0.0", "pug": "^3.0.0", "sass": "^1.26.8", "stylus": "^0.55.0", "sugarss": "^2.0.0 || ^3.0.0 || ^4.0.0", "svelte": "^3.23.0", "typescript": ">=3.9.5 || ^4.0.0 || ^5.0.0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "coffeescript": {"optional": true}, "less": {"optional": true}, "postcss": {"optional": true}, "postcss-load-config": {"optional": true}, "pug": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/svelte-check/node_modules/typescript": {"version": "4.9.5", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/svelte-hmr": {"version": "0.15.2", "license": "ISC", "engines": {"node": "^12.20 || ^14.13.1 || >= 16"}, "peerDependencies": {"svelte": "^3.19.0 || ^4.0.0-next.0"}}, "node_modules/svelte-imask": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/svelte-imask/-/svelte-imask-1.2.0.tgz", "integrity": "sha512-5LNUhokQ3rG3jkjIxewI2VIxoOYdrBCcnLIUddA9qmIO9dhDH+Qs2FkKm7wRsZy3x6WbwOjB4EvEoTMR8KrFqg==", "dependencies": {"imask": "latest"}}, "node_modules/svelte-pixi": {"version": "0.1.3", "license": "MIT", "peerDependencies": {"pixi.js": "^6.0.0"}}, "node_modules/svg-pathdata": {"version": "6.0.3", "license": "MIT", "optional": true, "engines": {"node": ">=12.0.0"}}, "node_modules/tailwind-scrollbar-hide": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/tailwind-scrollbar-hide/-/tailwind-scrollbar-hide-1.3.1.tgz", "integrity": "sha512-eUAvPTltKnAGHbCBRpOk5S7+UZTkFZgDKmZLZ6jZXXs4V7mRXvwshBjeMwrv3vmiWqm3IGEDFVKzUSm1JuoXKw==", "peerDependencies": {"tailwindcss": ">=3.0.0 || >= 4.0.0 || >= 4.0.0-beta.8 || >= 4.0.0-alpha.20"}}, "node_modules/tailwindcss": {"version": "3.4.16", "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.6.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.2", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "micromatch": "^4.0.8", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "resolve": "^1.22.8", "sucrase": "^3.35.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/tailwindcss/node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/tailwindcss/node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/tailwindcss/node_modules/fast-glob": {"version": "3.3.2", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/tailwindcss/node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/tailwindcss/node_modules/fast-glob/node_modules/micromatch": {"version": "4.0.5", "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/tailwindcss/node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/tailwindcss/node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/tailwindcss/node_modules/is-core-module": {"version": "2.11.0", "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tailwindcss/node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/tailwindcss/node_modules/micromatch/node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/tailwindcss/node_modules/nanoid": {"version": "3.3.8", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/tailwindcss/node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/tailwindcss/node_modules/postcss": {"version": "8.4.49", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/tailwindcss/node_modules/postcss-import": {"version": "15.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/tailwindcss/node_modules/postcss-import/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tailwindcss/node_modules/postcss-js": {"version": "4.0.1", "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/tailwindcss/node_modules/postcss-load-config": {"version": "4.0.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/tailwindcss/node_modules/postcss-nested": {"version": "6.2.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/tailwindcss/node_modules/resolve": {"version": "1.22.8", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tailwindcss/node_modules/resolve/node_modules/is-core-module": {"version": "2.15.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tailwindcss/node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/text-segmentation": {"version": "1.0.3", "license": "MIT", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/tinybench": {"version": "2.4.0", "dev": true, "license": "MIT"}, "node_modules/tinypool": {"version": "0.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/totalist": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/ts-interface-checker": {"version": "0.1.13", "license": "Apache-2.0"}, "node_modules/ts-poet": {"version": "6.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"dprint-node": "^1.0.7"}}, "node_modules/ts-proto": {"version": "1.146.0", "dev": true, "license": "ISC", "dependencies": {"@types/object-hash": "^1.3.0", "case-anything": "^2.1.10", "dataloader": "^1.4.0", "object-hash": "^1.3.1", "protobufjs": "^6.11.3", "ts-poet": "^6.4.1", "ts-proto-descriptors": "1.8.0"}, "bin": {"protoc-gen-ts_proto": "protoc-gen-ts_proto"}}, "node_modules/ts-proto-descriptors": {"version": "1.8.0", "dev": true, "license": "ISC", "dependencies": {"long": "^4.0.0", "protobufjs": "^6.8.8"}}, "node_modules/ts-proto/node_modules/object-hash": {"version": "1.3.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10.0"}}, "node_modules/tsconfig-paths": {"version": "3.14.2", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tslib": {"version": "2.5.0", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/turndown": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/turndown/-/turndown-7.2.0.tgz", "integrity": "sha512-eCZGBN4nNNqM9Owkv9HAtWRYfLA4h909E/WGAWWBpmB275ehNhZyk87/Tpvjbp0jjNl9XwCsbe6bm6CqFsgD+A==", "dependencies": {"@mixmark-io/domino": "^2.2.0"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-length": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.0.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "node_modules/uc.micro": {"version": "1.0.6", "license": "MIT"}, "node_modules/unbox-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici": {"version": "5.22.1", "license": "MIT", "dependencies": {"busboy": "^1.6.0"}, "engines": {"node": ">=14.0"}}, "node_modules/unplugin": {"version": "1.0.1", "license": "MIT", "dependencies": {"acorn": "^8.8.1", "chokidar": "^3.5.3", "webpack-sources": "^3.2.3", "webpack-virtual-modules": "^0.5.0"}}, "node_modules/update-browserslist-db": {"version": "1.0.10", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}], "license": "MIT", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "bin": {"browserslist-lint": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util": {"version": "0.12.5", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utrie": {"version": "1.0.2", "license": "MIT", "dependencies": {"base64-arraybuffer": "^1.0.2"}}, "node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/validator": {"version": "13.12.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vite": {"version": "4.2.1", "license": "MIT", "dependencies": {"esbuild": "^0.17.5", "postcss": "^8.4.21", "resolve": "^1.22.1", "rollup": "^3.18.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vitefu": {"version": "0.2.4", "license": "MIT", "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "peerDependenciesMeta": {"vite": {"optional": true}}}, "node_modules/vitest": {"version": "0.25.8", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^4.3.4", "@types/chai-subset": "^1.3.3", "@types/node": "*", "acorn": "^8.8.1", "acorn-walk": "^8.2.0", "chai": "^4.3.7", "debug": "^4.3.4", "local-pkg": "^0.4.2", "source-map": "^0.6.1", "strip-literal": "^1.0.0", "tinybench": "^2.3.1", "tinypool": "^0.3.0", "tinyspy": "^1.0.2", "vite": "^3.0.0 || ^4.0.0"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": ">=v14.16.0"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@edge-runtime/vm": "*", "@vitest/browser": "*", "@vitest/ui": "*", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/web-vitals": {"version": "3.5.2", "license": "Apache-2.0"}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/webpack-sources": {"version": "3.2.3", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-virtual-modules": {"version": "0.5.0", "license": "MIT"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-module": {"version": "2.0.1", "license": "ISC"}, "node_modules/which-typed-array": {"version": "1.1.9", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wmf": {"version": "1.0.2", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word": {"version": "0.3.0", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word-wrap": {"version": "1.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/wrap-ansi/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/ws": {"version": "8.17.1", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xlsx": {"version": "0.18.5", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "bin": {"xlsx": "bin/xlsx.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/xmlhttprequest-ssl": {"version": "2.0.0", "engines": {"node": ">=0.4.0"}}, "node_modules/y18n": {"version": "4.0.3", "license": "ISC"}, "node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.6.1", "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/yargs": {"version": "15.4.1", "license": "MIT", "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "engines": {"node": ">=8"}}, "node_modules/yargs-parser": {"version": "18.1.3", "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "engines": {"node": ">=6"}}, "node_modules/yargs/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yargs/node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}