import { browser } from '$app/environment';
import { env } from '$env/dynamic/public';
const { PUBLIC_API_HOST } = env;
import { errorMessages } from '$lib/stores/notifications';
import { accessToken } from '$lib/stores/tokens';
import * as Sentry from '@sentry/sveltekit';
import Cookies from 'js-cookie';

let currentTokenInStore: string | null = null;
accessToken.subscribe((v) => {
  currentTokenInStore = v;
});

export type CallError = {
  type: string;
  message: string;
};

const getAuthHeader = (token: string | false | null): HeadersInit =>
  token
    ? {
        Authorization: `Bearer ${token}`
      }
    : {};

const getContentHeader = (body: any | null): HeadersInit =>
  body
    ? {
        'Content-Type': 'application/json'
      }
    : {};

export function isCallError<T>(data: CallError | T, supressError = false): data is CallError {
  const isError =
    (data as CallError).type !== undefined && (data as CallError).message !== undefined;
  if (!isError) return false;
  if (!supressError) {
    errorMessages.update((v) => [...v, (data as CallError).message]);
  }
  Sentry.addBreadcrumb({
    category: 'fetch',
    message: 'Error in isCallError',
    level: 'info',
    data: {
      data
    },
    timestamp: Date.now()
  });
  Sentry.captureMessage('API call failed', 'error');
  Sentry.flush(); //seems like not all errors are lgging in sentry
  return true;
}

export default async function call<V, T>(
  url: string,
  method: 'GET' | 'PUT' | 'POST' | 'PATCH' | 'DELETE',
  needsAuth: boolean,
  body: V | null = null,
  token?: string,
  host = 'http://localhost:50040'
): Promise<T | CallError> {
  console.log({ host });
  //token is passed from get requests in page.ts or layout.ts files, if that doesn't exist then use currentTokenInStore which is set on user login
  if (needsAuth && !token && !currentTokenInStore) {
    if (browser) {
      const token = Cookies.get('accessToken');
      if (token) {
        currentTokenInStore = token;
        accessToken.set(token);
      }
      console.log({ token });
    } else {
      return {
        type: 'NO_TOKEN_FOR_AUTH_CALL',
        message: 'You are not logged in and need to be to access this data.'
      };
    }
  }
  console.log({ url: `${host}${url}` });
  try {
    if (method === 'GET' && body) console.warn('Attempting to call a get method with a body');
    const res = await fetch(`${host}${url}`, {
      method,
      headers: {
        ...getAuthHeader(needsAuth && token ? token : currentTokenInStore),
        ...getContentHeader(body)
      },
      body: body ? JSON.stringify(body) : undefined
    });
    if (res.ok) {
      return res.json() as T;
    }
    const contentType = res.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const resData = await res.json();
      if (!resData.error)
        return {
          type: 'ERROR_RESPONSE_INVALID',
          message: 'The error response was not structured in a way we expected'
        };
      const { error } = resData;
      const keys = Object.keys(error);
      if (keys.includes('type') && keys.includes('message')) {
        return {
          type: error.type,
          message: error.message
        };
      } else if (Array.isArray(error)) {
        return {
          type: 'OLD_ERROR_FORMAT',
          message: (error as string[]).join(',')
        };
      }
    }
    return {
      type: 'UNKNOWN_RETURN',
      message: 'The call returned an unexpected content type'
    };
  } catch (err) {
    Sentry.captureException(err);
    return {
      type: 'CALL_FAILED',
      message: 'The client side call failed, something errored.'
    };
  }
}
