# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@airwallex/airtracker@1.2.0":
  version "1.2.0"
  dependencies:
    web-vitals "^3.5.1"

"@airwallex/components-sdk@^1.7.1":
  version "1.7.1"
  dependencies:
    "@airwallex/airtracker" "1.2.0"

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"

"@babel/helper-string-parser@^7.22.5":
  version "7.22.5"

"@babel/helper-validator-identifier@^7.22.5":
  version "7.22.5"

"@babel/parser@^7.21.9":
  version "7.22.7"

"@babel/runtime-corejs3@^7.24.4":
  version "7.28.0"
  resolved "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.28.0.tgz"
  integrity sha512-nlIXnSqLcBij8K8TtkxbBJgfzfvi75V1pAKSM7dUXejGw12vJAqez74jZrHTsJ3Z+Aczc5Q/6JgNjKRMsVU44g==
  dependencies:
    core-js-pure "^3.43.0"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.14.0":
  version "7.22.6"
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/types@^7.21.5":
  version "7.22.5"
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    to-fast-properties "^2.0.0"

"@esbuild/darwin-x64@0.17.14":
  version "0.17.14"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  version "4.5.0"

"@eslint/eslintrc@^2.0.2":
  version "2.0.2"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.5.1"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.37.0":
  version "8.37.0"

"@googlemaps/js-api-loader@^1.16.2":
  version "1.16.2"
  dependencies:
    fast-deep-equal "^3.1.3"

"@graphql-typed-document-node/core@^3.2.0":
  version "3.2.0"

"@humanwhocodes/config-array@^0.11.8":
  version "0.11.8"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    debug "^4.1.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"

"@humanwhocodes/object-schema@^1.2.1":
  version "1.2.1"

"@imask/svelte@^7.1.3":
  version "7.6.1"
  resolved "https://registry.npmjs.org/@imask/svelte/-/svelte-7.6.1.tgz"
  integrity sha512-NVGhgV+endyrmG8srUmNjbULmZs8Wq9B7YFzV/jKtC3EYWhMzccgvQ27IEkRFQ9dcxaPjmwni9UejfrkK1oeow==
  dependencies:
    imask "^7.6.1"

"@intercom/messenger-js-sdk@^0.0.12":
  version "0.0.12"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.5"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.5.0"

"@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"

"@jridgewell/sourcemap-codec@^1.4.15":
  version "1.4.15"

"@jridgewell/trace-mapping@^0.3.17":
  version "0.3.17"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@jridgewell/trace-mapping@^0.3.24":
  version "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@kurkle/color@^0.3.0":
  version "0.3.2"

"@mixmark-io/domino@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@mixmark-io/domino/-/domino-2.2.0.tgz"
  integrity sha512-Y28PR25bHXUg88kCV7nivXrP2Nj2RueZ3/l/jdx6J9f8J4nsEGcgX0Qe6lt7Pa+J79+kPiJU3LguR6O/6zrLOw==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pixi/colord@^2.9.6":
  version "2.9.6"
  resolved "https://registry.npmjs.org/@pixi/colord/-/colord-2.9.6.tgz"
  integrity sha512-nezytU2pw587fQstUu1AsJZDVEynjskwOL+kibwcdxsMBFqPsFFNA7xl0ii/gXuDi6M0xj3mfRJj8pBSc2jCfA==

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"

"@playwright/test@^1.36.0":
  version "1.36.0"
  dependencies:
    "@types/node" "*"
    playwright-core "1.36.0"
  optionalDependencies:
    fsevents "2.3.2"

"@polka/url@^1.0.0-next.20":
  version "1.0.0-next.21"

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"

"@protobufjs/base64@^1.1.2":
  version "1.1.2"

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"

"@protobufjs/path@^1.1.2":
  version "1.1.2"

"@protobufjs/pool@^1.1.0":
  version "1.1.0"

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"

"@rollup/plugin-commonjs@^24.0.0":
  version "24.0.1"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    commondir "^1.0.1"
    estree-walker "^2.0.2"
    glob "^8.0.3"
    is-reference "1.2.1"
    magic-string "^0.27.0"

"@rollup/plugin-json@^6.0.0":
  version "6.0.0"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"

"@rollup/plugin-node-resolve@^15.0.1":
  version "15.0.1"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    "@types/resolve" "1.20.2"
    deepmerge "^4.2.2"
    is-builtin-module "^3.2.0"
    is-module "^1.0.0"
    resolve "^1.22.1"

"@rollup/pluginutils@^5.0.1":
  version "5.0.2"
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^2.3.1"

"@sentry-internal/tracing@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/core" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/browser@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry-internal/tracing" "7.57.0"
    "@sentry/core" "7.57.0"
    "@sentry/replay" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/bundler-plugin-core@0.6.0":
  version "0.6.0"
  dependencies:
    "@sentry/cli" "^2.17.0"
    "@sentry/node" "^7.19.0"
    "@sentry/tracing" "^7.19.0"
    find-up "5.0.0"
    glob "9.3.2"
    magic-string "0.27.0"
    unplugin "1.0.1"
    webpack-sources "3.2.3"

"@sentry/cli@^2.17.0":
  version "2.19.4"
  dependencies:
    https-proxy-agent "^5.0.0"
    node-fetch "^2.6.7"
    progress "^2.0.3"
    proxy-from-env "^1.1.0"
    which "^2.0.2"

"@sentry/core@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/integrations@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    localforage "^1.8.1"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/node@^7.19.0", "@sentry/node@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry-internal/tracing" "7.57.0"
    "@sentry/core" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    cookie "^0.4.1"
    https-proxy-agent "^5.0.0"
    lru_map "^0.3.3"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/replay@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/core" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"

"@sentry/svelte@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/browser" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    magic-string "^0.30.0"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/sveltekit@^7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry-internal/tracing" "7.57.0"
    "@sentry/core" "7.57.0"
    "@sentry/integrations" "7.57.0"
    "@sentry/node" "7.57.0"
    "@sentry/svelte" "7.57.0"
    "@sentry/types" "7.57.0"
    "@sentry/utils" "7.57.0"
    "@sentry/vite-plugin" "^0.6.0"
    magicast "0.2.8"
    sorcery "0.11.0"

"@sentry/tracing@^7.19.0":
  version "7.57.0"
  dependencies:
    "@sentry-internal/tracing" "7.57.0"

"@sentry/types@7.57.0":
  version "7.57.0"

"@sentry/utils@7.57.0":
  version "7.57.0"
  dependencies:
    "@sentry/types" "7.57.0"
    tslib "^2.4.1 || ^1.9.3"

"@sentry/vite-plugin@^0.6.0":
  version "0.6.0"
  dependencies:
    "@sentry/bundler-plugin-core" "0.6.0"

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"

"@svelte-put/clickoutside@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@svelte-put/clickoutside/-/clickoutside-2.0.0.tgz"
  integrity sha512-04xYxOT5Ti+VlP70+uY/a5tGnVWl3XFgnRiO9bMSHbkwOlzHxSgXyM1NCS4NV09B3i41LRuShgeMrbgU0J5cPg==

"@sveltejs/adapter-auto@^2.0.0":
  version "2.0.0"
  dependencies:
    import-meta-resolve "^2.2.0"

"@sveltejs/adapter-node@^1.2.3":
  version "1.2.3"
  dependencies:
    "@rollup/plugin-commonjs" "^24.0.0"
    "@rollup/plugin-json" "^6.0.0"
    "@rollup/plugin-node-resolve" "^15.0.1"
    rollup "^3.7.0"

"@sveltejs/kit@^1.0.0", "@sveltejs/kit@^1.15.2", "@sveltejs/kit@1.x":
  version "1.22.4"
  dependencies:
    "@sveltejs/vite-plugin-svelte" "^2.4.1"
    "@types/cookie" "^0.5.1"
    cookie "^0.5.0"
    devalue "^4.3.1"
    esm-env "^1.0.0"
    kleur "^4.1.5"
    magic-string "^0.30.0"
    mime "^3.0.0"
    sade "^1.8.1"
    set-cookie-parser "^2.6.0"
    sirv "^2.0.2"
    undici "~5.22.0"

"@sveltejs/vite-plugin-svelte-inspector@^1.0.3":
  version "1.0.3"
  dependencies:
    debug "^4.3.4"

"@sveltejs/vite-plugin-svelte@^2.2.0", "@sveltejs/vite-plugin-svelte@^2.4.1":
  version "2.4.3"
  dependencies:
    "@sveltejs/vite-plugin-svelte-inspector" "^1.0.3"
    debug "^4.3.4"
    deepmerge "^4.3.1"
    kleur "^4.1.5"
    magic-string "^0.30.1"
    svelte-hmr "^0.15.2"
    vitefu "^0.2.4"

"@tangopay/tango-cloud-grpc-proto@2.6.22":
  version "2.6.22"
  resolved "https://npm.pkg.github.com/download/@tangopay/tango-cloud-grpc-proto/2.6.22/5ee60f6dec7a7514b643e8ef1ac0d90178b96148"
  integrity sha512-ZWFXjJ4dbiCXztpnvhWJfEJ4cksacdrS40wlUhdAT89jIHmhy5IraGUQDu54Y5bXwxn28Ef6tLqnDoKKllzT0g==

"@tangopay/tango-svelte-library-v2@2.1.21":
  version "2.1.21"
  resolved "https://npm.pkg.github.com/download/@tangopay/tango-svelte-library-v2/2.1.21/ef799daeb1663811c345d8d54a34d74efe83c9f1"
  integrity sha512-cwUHKRJxiXDuo30RAli5I1Agyo4HHCpBcDpYOCl6QViHLBl/PbNbn6B6mfIJwYq55ofmyxxCTcAXyXegoS079Q==
  dependencies:
    "@imask/svelte" "^7.1.3"
    "@svelte-put/clickoutside" "^2.0.0"
    csv42 "^3.0.3"
    dayjs "^1.11.7"
    emoji-picker-element "^1.18.3"
    lodash "^4.17.21"
    markdown-it "^13.0.1"
    numeral "^2.0.6"
    quill "1.3.6"
    svelte-imask "^1.2.0"
    tailwind-scrollbar-hide "^1.1.7"
    turndown "^7.1.2"
    uuid "^9.0.1"
    validator "^13.9.0"

"@tangopay/tango-unity@1.1.12":
  version "1.1.12"
  resolved "https://npm.pkg.github.com/download/@tangopay/tango-unity/1.1.12/9a974f95cc3460582f3dc13d007b64ad62fb791f"
  integrity sha512-1fIyIqalKf8kPjv8ULTjmGjXjj5EfQLCBUQWJBS/lOG/MnO2onr80a30W3hFBM2f8gAdoxptgjryXua98F3+0w==
  dependencies:
    dayjs "^1.11.8"
    jspdf "^2.5.1"
    jspdf-autotable "^3.5.29"
    lodash "^4.17.21"
    numeral "^2.0.6"
    validator "^13.9.0"

"@types/animejs@^3.1.12":
  version "3.1.12"

"@types/chai-subset@^1.3.3":
  version "1.3.3"
  dependencies:
    "@types/chai" "*"

"@types/chai@*", "@types/chai@^4.3.4":
  version "4.3.4"

"@types/chance@^1.1.6":
  version "1.1.6"

"@types/color-convert@*":
  version "2.0.4"
  dependencies:
    "@types/color-name" "^1.1.0"

"@types/color-name@^1.1.0":
  version "1.1.5"

"@types/color@^4.2.0":
  version "4.2.0"
  dependencies:
    "@types/color-convert" "*"

"@types/cookie@^0.5.1":
  version "0.5.1"

"@types/css-font-loading-module@^0.0.12":
  version "0.0.12"
  resolved "https://registry.npmjs.org/@types/css-font-loading-module/-/css-font-loading-module-0.0.12.tgz"
  integrity sha512-x2tZZYkSxXqWvTDgveSynfjq/T2HyiZHXb00j/+gy19yp70PHCizM48XFdjBCWH7eHBD0R5i/pw9yMBP/BH5uA==

"@types/earcut@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/earcut/-/earcut-3.0.0.tgz"
  integrity sha512-k/9fOUGO39yd2sCjrbAJvGDEQvRwRnQIZlBz43roGwUZo5SHAmyVvSFyaVVZkicRVCaDXPKlbxrUcBuJoSWunQ==

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.0"

"@types/google.maps@^3.53.4":
  version "3.53.4"

"@types/js-cookie@^3.0.3":
  version "3.0.3"

"@types/json-schema@^7.0.9":
  version "7.0.11"

"@types/json5@^0.0.29":
  version "0.0.29"

"@types/linkify-it@*":
  version "3.0.2"

"@types/lodash@^4.14.195":
  version "4.14.195"

"@types/long@^4.0.1":
  version "4.0.2"

"@types/markdown-it@^12.2.3":
  version "12.2.3"
  dependencies:
    "@types/linkify-it" "*"
    "@types/mdurl" "*"

"@types/mdurl@*":
  version "1.0.2"

"@types/node@*", "@types/node@>= 14", "@types/node@>=13.7.0":
  version "18.15.11"

"@types/numeral@^2.0.2":
  version "2.0.2"

"@types/object-hash@^1.3.0":
  version "1.3.4"

"@types/pixi.js@^5.0.0":
  version "5.0.0"
  dependencies:
    pixi.js "*"

"@types/plaid-link@^2.0.15":
  version "2.0.15"

"@types/pug@^2.0.6":
  version "2.0.6"

"@types/qrcode@^1.5.5":
  version "1.5.5"
  dependencies:
    "@types/node" "*"

"@types/raf@^3.4.0":
  version "3.4.0"

"@types/resolve@1.20.2":
  version "1.20.2"

"@types/semver@^7.3.12":
  version "7.3.13"

"@types/uuid@^9.0.7":
  version "9.0.7"

"@types/validator@^13.12.1":
  version "13.12.1"

"@typescript-eslint/eslint-plugin@^5.45.0":
  version "5.57.0"
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/type-utils" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    debug "^4.3.4"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.45.0":
  version "5.57.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/typescript-estree" "5.57.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.57.0":
  version "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"

"@typescript-eslint/type-utils@5.57.0":
  version "5.57.0"
  dependencies:
    "@typescript-eslint/typescript-estree" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.57.0":
  version "5.57.0"

"@typescript-eslint/typescript-estree@5.57.0":
  version "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.57.0":
  version "5.57.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/typescript-estree" "5.57.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.57.0":
  version "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    eslint-visitor-keys "^3.3.0"

"@webgpu/types@^0.1.40":
  version "0.1.63"
  resolved "https://registry.npmjs.org/@webgpu/types/-/types-0.1.63.tgz"
  integrity sha512-s9Kuh0nE/2+nKrvmKNMB2fE5Zlr3DL2t3OFKM55v5jRcfCOxbkOHhQoshoFum5mmXIfEtRXtLCWmkeTJsVjE9w==

"@xmldom/xmldom@^0.8.10":
  version "0.8.10"
  resolved "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz"
  integrity sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==

acorn-jsx@^5.3.2:
  version "5.3.2"

acorn-walk@^8.2.0:
  version "8.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.8.0, acorn@^8.8.1, acorn@^8.8.2:
  version "8.8.2"

adler-32@~1.3.0:
  version "1.3.1"

agent-base@6:
  version "6.0.2"
  dependencies:
    debug "4"

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

animejs@^3.2.2:
  version "3.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"

ansi-regex@^6.0.1:
  version "6.1.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"

any-promise@^1.0.0:
  version "1.3.0"

anymatch@~3.1.2:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"

argparse@^2.0.1:
  version "2.0.1"

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-includes@^3.1.6:
  version "3.1.6"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"

array.prototype.flat@^1.3.1:
  version "1.3.1"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1:
  version "1.3.1"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

assert@^2.0.0:
  version "2.0.0"
  dependencies:
    es6-object-assign "^1.1.0"
    is-nan "^1.2.1"
    object-is "^1.0.1"
    util "^0.12.0"

assertion-error@^1.1.0:
  version "1.1.0"

ast-types@^0.16.1:
  version "0.16.1"
  dependencies:
    tslib "^2.0.1"

atob@^2.1.2:
  version "2.1.2"

autoprefixer@^10.4.14:
  version "10.4.14"
  dependencies:
    browserslist "^4.21.5"
    caniuse-lite "^1.0.30001464"
    fraction.js "^4.2.0"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.5:
  version "1.0.5"

balanced-match@^1.0.0:
  version "1.0.2"

base64-arraybuffer@^1.0.2:
  version "1.0.2"

binary-extensions@^2.0.0:
  version "2.2.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  dependencies:
    fill-range "^7.0.1"

braces@^3.0.3:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.21.5, "browserslist@>= 4.21.0":
  version "4.21.5"
  dependencies:
    caniuse-lite "^1.0.30001449"
    electron-to-chromium "^1.4.284"
    node-releases "^2.0.8"
    update-browserslist-db "^1.0.10"

btoa@^1.2.1:
  version "1.2.1"

buffer-crc32@^0.2.5:
  version "0.2.13"

builtin-modules@^3.3.0:
  version "3.3.0"

busboy@^1.6.0:
  version "1.6.0"
  dependencies:
    streamsearch "^1.1.0"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"

camelcase-css@^2.0.1:
  version "2.0.1"

camelcase@^5.0.0:
  version "5.3.1"

caniuse-lite@^1.0.30001449, caniuse-lite@^1.0.30001464:
  version "1.0.30001473"

canvg@^3.0.6:
  version "3.0.10"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    core-js "^3.8.3"
    raf "^3.4.1"
    regenerator-runtime "^0.13.7"
    rgbcolor "^1.0.1"
    stackblur-canvas "^2.0.0"
    svg-pathdata "^6.0.3"

case-anything@^2.1.10:
  version "2.1.10"

cfb@~1.2.1:
  version "1.2.2"
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chai@^4.3.7:
  version "4.3.7"
  dependencies:
    assertion-error "^1.1.0"
    check-error "^1.0.2"
    deep-eql "^4.1.2"
    get-func-name "^2.0.0"
    loupe "^2.3.1"
    pathval "^1.1.1"
    type-detect "^4.0.5"

chalk@^4.0.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chance@^1.1.12:
  version "1.1.12"

"chart.js@^3.5.0 || ^4.0.0", chart.js@^4.4.0, chart.js@>=4.0.1:
  version "4.4.0"
  dependencies:
    "@kurkle/color" "^0.3.0"

chartjs-adapter-dayjs-4@^1.0.4:
  version "1.0.4"

check-error@^1.0.2:
  version "1.0.2"

chokidar@^3.4.1, chokidar@^3.5.3:
  version "3.5.3"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^3.6.0:
  version "3.6.0"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

cliui@^6.0.0:
  version "6.0.0"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

codepage@~1.15.0:
  version "1.15.0"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"

color-string@^1.9.0:
  version "1.9.1"
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.3:
  version "4.2.3"
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

commander@^4.0.0:
  version "4.1.1"

commondir@^1.0.1:
  version "1.0.1"

concat-map@0.0.1:
  version "0.0.1"

cookie@^0.4.1:
  version "0.4.2"

cookie@^0.5.0:
  version "0.5.0"

core-js-pure@^3.43.0:
  version "3.43.0"
  resolved "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.43.0.tgz"
  integrity sha512-i/AgxU2+A+BbJdMxh3v7/vxi2SbFqxiFmg6VsDwYB4jkucrd1BZNA9a9gphC0fYMG5IBSgQcbQnk865VCLe7xA==

core-js@^3.6.0, core-js@^3.8.3:
  version "3.31.1"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"

cross-fetch@^3.1.5:
  version "3.1.5"
  dependencies:
    node-fetch "2.6.7"

cross-spawn@^7.0.0:
  version "7.0.6"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cross-spawn@^7.0.2:
  version "7.0.3"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-line-break@^2.1.0:
  version "2.1.0"
  dependencies:
    utrie "^1.0.2"

cssesc@^3.0.0:
  version "3.0.0"

csv42@^3.0.3:
  version "3.0.4"
  resolved "https://registry.npmjs.org/csv42/-/csv42-3.0.4.tgz"
  integrity sha512-tLjSa9EiI5Gc/T/ux15wU6NU0Di+XqTd2P1CLHsSRr4jykpeLoPs9vPINNGCWzZEiSAAUv+i2q4pE/GOKnUXIQ==

dataloader@^1.4.0:
  version "1.4.0"

dayjs@^1.11.7, dayjs@^1.11.8, dayjs@^1.9.7:
  version "1.11.13"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz"
  integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.1.1, debug@^4.3.2, debug@^4.3.4, debug@4:
  version "4.3.4"
  dependencies:
    ms "2.1.2"

debug@~4.3.1:
  version "4.3.6"
  dependencies:
    ms "2.1.2"

debug@~4.3.2:
  version "4.3.6"
  dependencies:
    ms "2.1.2"

decamelize@^1.2.0:
  version "1.2.0"

deep-eql@^4.1.2:
  version "4.1.3"
  dependencies:
    type-detect "^4.0.0"

deep-equal@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz"
  integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@^0.1.3:
  version "0.1.4"

deepmerge@^4.2.2, deepmerge@^4.3.1:
  version "4.3.1"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

detect-indent@^6.1.0:
  version "6.1.0"

detect-libc@^1.0.3:
  version "1.0.3"

devalue@^4.3.1:
  version "4.3.2"

didyoumean@^1.2.2:
  version "1.2.2"

dijkstrajs@^1.0.1:
  version "1.0.3"

dir-glob@^3.0.1:
  version "3.0.1"
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"

doctrine@^2.1.0:
  version "2.1.0"
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  dependencies:
    esutils "^2.0.2"

dompurify@^2.2.0:
  version "2.4.6"

dotenv@^16.3.1:
  version "16.3.1"

dprint-node@^1.0.7:
  version "1.0.7"
  dependencies:
    detect-libc "^1.0.3"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

earcut@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/earcut/-/earcut-3.0.1.tgz"
  integrity sha512-0l1/0gOjESMeQyYaK5IDiPNvFeu93Z/cO0TjZh9eZ1vyCtZnA7KMZ8rQggpsJHIbGSdrqYq9OhuveadOVHCshw==

eastasianwidth@^0.2.0:
  version "0.2.0"

electron-to-chromium@^1.4.284:
  version "1.4.347"

emoji-picker-element@^1.18.3:
  version "1.26.3"
  resolved "https://registry.npmjs.org/emoji-picker-element/-/emoji-picker-element-1.26.3.tgz"
  integrity sha512-fOMG44d/3OqTe1pPqlu5H4ZtWg7gK4Le6Bt24JTKtDyce5+EO3Mo8WA95cKHbPSsSsg7ehM12M1x3Y6U6fgvTQ==

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

encode-utf8@^1.0.3:
  version "1.0.3"

encoding@^0.1.0:
  version "0.1.13"
  dependencies:
    iconv-lite "^0.6.2"

engine.io-client@~6.5.2:
  version "6.5.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"
    xmlhttprequest-ssl "~2.0.0"

engine.io-parser@~5.2.1:
  version "5.2.3"

entities@~3.0.1:
  version "3.0.1"

es-abstract@^1.19.0, es-abstract@^1.20.4:
  version "1.21.2"
  dependencies:
    array-buffer-byte-length "^1.0.0"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.2.0"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.10"
    is-weakref "^1.0.2"
    object-inspect "^1.12.3"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.7"
    string.prototype.trimend "^1.0.6"
    string.prototype.trimstart "^1.0.6"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.9"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.1:
  version "2.0.1"
  dependencies:
    get-intrinsic "^1.1.3"
    has "^1.0.3"
    has-tostringtag "^1.0.0"

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  dependencies:
    has "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es6-object-assign@^1.1.0:
  version "1.1.0"

es6-promise@^3.1.2:
  version "3.3.1"

esbuild@^0.17.5:
  version "0.17.14"
  optionalDependencies:
    "@esbuild/android-arm" "0.17.14"
    "@esbuild/android-arm64" "0.17.14"
    "@esbuild/android-x64" "0.17.14"
    "@esbuild/darwin-arm64" "0.17.14"
    "@esbuild/darwin-x64" "0.17.14"
    "@esbuild/freebsd-arm64" "0.17.14"
    "@esbuild/freebsd-x64" "0.17.14"
    "@esbuild/linux-arm" "0.17.14"
    "@esbuild/linux-arm64" "0.17.14"
    "@esbuild/linux-ia32" "0.17.14"
    "@esbuild/linux-loong64" "0.17.14"
    "@esbuild/linux-mips64el" "0.17.14"
    "@esbuild/linux-ppc64" "0.17.14"
    "@esbuild/linux-riscv64" "0.17.14"
    "@esbuild/linux-s390x" "0.17.14"
    "@esbuild/linux-x64" "0.17.14"
    "@esbuild/netbsd-x64" "0.17.14"
    "@esbuild/openbsd-x64" "0.17.14"
    "@esbuild/sunos-x64" "0.17.14"
    "@esbuild/win32-arm64" "0.17.14"
    "@esbuild/win32-ia32" "0.17.14"
    "@esbuild/win32-x64" "0.17.14"

escalade@^3.1.1:
  version "3.1.1"

escape-string-regexp@^4.0.0:
  version "4.0.0"

eslint-config-prettier@^8.5.0:
  version "8.8.0"

eslint-import-resolver-node@^0.3.7:
  version "0.3.7"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.11.0"
    resolve "^1.22.1"

eslint-module-utils@^2.7.4:
  version "2.8.0"
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@^2.27.5:
  version "2.27.5"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    array.prototype.flatmap "^1.3.1"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.7"
    eslint-module-utils "^2.7.4"
    has "^1.0.3"
    is-core-module "^2.11.0"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.6"
    resolve "^1.22.1"
    semver "^6.3.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-svelte3@^4.0.0:
  version "4.0.0"

eslint-scope@^5.1.1:
  version "5.1.1"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.1.1:
  version "7.1.1"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.0:
  version "3.4.0"

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", eslint@^8.28.0, eslint@>=7.0.0, eslint@>=8.0.0:
  version "8.37.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.4.0"
    "@eslint/eslintrc" "^2.0.2"
    "@eslint/js" "8.37.0"
    "@humanwhocodes/config-array" "^0.11.8"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.4.0"
    espree "^9.5.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-sdsl "^4.1.4"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"

esm-env@^1.0.0:
  version "1.0.0"

espree@^9.5.1:
  version "9.5.1"
  dependencies:
    acorn "^8.8.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.0"

esprima@~4.0.0:
  version "4.0.1"

esquery@^1.4.2:
  version "1.5.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"

estree-walker@^2.0.2:
  version "2.0.2"

esutils@^2.0.2:
  version "2.0.3"

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz"
  integrity sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

extend@^3.0.1, extend@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"

fast-diff@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz"
  integrity sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.2.12"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-glob@^3.3.2:
  version "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"

fast-levenshtein@^2.0.6:
  version "2.0.6"

fastq@^1.6.0:
  version "1.15.0"
  dependencies:
    reusify "^1.0.4"

fflate@^0.4.8:
  version "0.4.8"

file-entry-cache@^6.0.1:
  version "6.0.1"
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.0.1:
  version "7.0.1"
  dependencies:
    to-regex-range "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

find-up@^4.1.0:
  version "4.1.0"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0, find-up@5.0.0:
  version "5.0.0"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"

for-each@^0.3.3:
  version "0.3.3"
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.3.0"
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

frac@~1.1.2:
  version "1.1.2"

fraction.js@^4.2.0:
  version "4.2.0"

fs.realpath@^1.0.0:
  version "1.0.0"

fsevents@~2.3.2, fsevents@2.3.2:
  version "2.3.2"

function-bind@^1.1.1, function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.5:
  version "1.1.5"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functions-have-names@^1.2.2, functions-have-names@^1.2.3:
  version "1.2.3"

get-caller-file@^2.0.1:
  version "2.0.5"

get-func-name@^2.0.0:
  version "2.0.0"

get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0, get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-symbol-description@^1.0.0:
  version "1.0.0"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

gifuct-js@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/gifuct-js/-/gifuct-js-2.1.2.tgz"
  integrity sha512-rI2asw77u0mGgwhV3qA+OEgYqaDn5UNqgs+Bx0FGwSpuqfYn+Ir6RQY5ENNQ8SbIiG/m5gVa7CD5RriO4f4Lsg==
  dependencies:
    js-binary-schema-parser "^2.0.3"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.3:
  version "8.1.0"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

glob@9.3.2:
  version "9.3.2"
  dependencies:
    fs.realpath "^1.0.0"
    minimatch "^7.4.1"
    minipass "^4.2.4"
    path-scurry "^1.6.1"

globals@^13.19.0:
  version "13.20.0"
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

gql-query-builder@^3.8.0:
  version "3.8.0"

graceful-fs@^4.1.3:
  version "4.2.11"

grapheme-splitter@^1.0.4:
  version "1.0.4"

graphql-request@^6.0.0:
  version "6.0.0"
  dependencies:
    "@graphql-typed-document-node/core" "^3.2.0"
    cross-fetch "^3.1.5"

graphql-tag@^2.12.6:
  version "2.12.6"
  dependencies:
    tslib "^2.1.0"

"graphql@^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "graphql@^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", graphql@^16.6.0, "graphql@14 - 16":
  version "16.6.0"

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.1"

has-symbols@^1.0.2, has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.0:
  version "1.0.0"
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  dependencies:
    function-bind "^1.1.1"

hash.js@^1.1.7:
  version "1.1.7"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

html2canvas@^1.0.0-rc.5, html2canvas@^1.4.1:
  version "1.4.1"
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  dependencies:
    agent-base "6"
    debug "4"

iconv-lite@^0.6.2:
  version "0.6.3"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ignore@^5.2.0:
  version "5.2.4"

imask@^7.6.1, imask@latest:
  version "7.6.1"
  resolved "https://registry.npmjs.org/imask/-/imask-7.6.1.tgz"
  integrity sha512-sJlIFM7eathUEMChTh9Mrfw/IgiWgJqBKq2VNbyXvBZ7ev/IlO6/KQTKlV/Fm+viQMLrFLG/zCuudrLIwgK2dg==
  dependencies:
    "@babel/runtime-corejs3" "^7.24.4"

immediate@~3.0.5:
  version "3.0.6"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-meta-resolve@^2.2.0:
  version "2.2.2"

imurmurhash@^0.1.4:
  version "0.1.4"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@2:
  version "2.0.4"

internal-slot@^1.0.5:
  version "1.0.5"
  dependencies:
    get-intrinsic "^1.2.0"
    has "^1.0.3"
    side-channel "^1.0.4"

is-arguments@^1.0.4, is-arguments@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.3.1:
  version "0.3.2"

is-bigint@^1.0.1:
  version "1.0.4"
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-builtin-module@^3.2.0:
  version "3.2.1"
  dependencies:
    builtin-modules "^3.3.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"

is-core-module@^2.11.0:
  version "2.12.0"
  dependencies:
    has "^1.0.3"

is-core-module@^2.13.0:
  version "2.15.1"
  dependencies:
    hasown "^2.0.2"

is-core-module@^2.9.0:
  version "2.11.0"
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1, is-date-object@^1.0.5:
  version "1.0.5"
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-generator-function@^1.0.7:
  version "1.0.10"
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-module@^1.0.0:
  version "1.0.0"

is-nan@^1.2.1:
  version "1.3.2"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

is-negative-zero@^2.0.2:
  version "2.0.2"

is-number-object@^1.0.4:
  version "1.0.7"
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"

is-path-inside@^3.0.3:
  version "3.0.3"

is-reference@1.2.1:
  version "1.2.1"
  dependencies:
    "@types/estree" "*"

is-regex@^1.1.4:
  version "1.1.4"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.3, is-typed-array@^1.1.9:
  version "1.1.10"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-weakref@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"

isexe@^2.0.0:
  version "2.0.0"

ismobilejs@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ismobilejs/-/ismobilejs-1.1.1.tgz"
  integrity sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw==

jackspeak@^3.1.2:
  version "3.4.3"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.21.6:
  version "1.21.6"

js-binary-schema-parser@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/js-binary-schema-parser/-/js-binary-schema-parser-2.0.3.tgz"
  integrity sha512-xezGJmOb4lk/M1ZZLTR/jaBHQ4gG/lqQnJqdIv4721DMggsa1bDVlHXNeHYogaIEHD9vCRv0fcL4hMA+Coarkg==

js-cookie@^3.0.5:
  version "3.0.5"

js-sdsl@^4.1.4:
  version "4.4.0"

js-yaml@^4.1.0:
  version "4.1.0"
  dependencies:
    argparse "^2.0.1"

json-schema-traverse@^0.4.1:
  version "0.4.1"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"

json5@^1.0.2:
  version "1.0.2"
  dependencies:
    minimist "^1.2.0"

jspdf-autotable@^3.5.29, jspdf-autotable@^3.8.2:
  version "3.8.2"

jspdf@^2.5.1:
  version "2.5.1"
  dependencies:
    "@babel/runtime" "^7.14.0"
    atob "^2.1.2"
    btoa "^1.2.1"
    fflate "^0.4.8"
  optionalDependencies:
    canvg "^3.0.6"
    core-js "^3.6.0"
    dompurify "^2.2.0"
    html2canvas "^1.0.0-rc.5"

jwt-decode@^3.0.0:
  version "3.1.2"

kleur@^4.1.5:
  version "4.1.5"

levn@^0.4.1:
  version "0.4.1"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@3.1.1:
  version "3.1.1"
  dependencies:
    immediate "~3.0.5"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"

lines-and-columns@^1.1.6:
  version "1.2.4"

linkify-it@^4.0.1:
  version "4.0.1"
  dependencies:
    uc.micro "^1.0.1"

local-pkg@^0.4.2:
  version "0.4.3"

localforage@^1.8.1:
  version "1.10.0"
  dependencies:
    lie "3.1.1"

locate-path@^5.0.0:
  version "5.0.0"
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  dependencies:
    p-locate "^5.0.0"

lodash.merge@^4.6.2:
  version "4.6.2"

lodash@^4.17.21:
  version "4.17.21"

long@^4.0.0:
  version "4.0.0"

loupe@^2.3.1:
  version "2.3.6"
  dependencies:
    get-func-name "^2.0.0"

lru_map@^0.3.3:
  version "0.3.3"

lru-cache@^10.2.0:
  version "10.4.3"

lru-cache@^6.0.0:
  version "6.0.0"
  dependencies:
    yallist "^4.0.0"

"lru-cache@^9.1.1 || ^10.0.0":
  version "10.0.0"

magic-string@^0.27.0, magic-string@0.27.0:
  version "0.27.0"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

magic-string@^0.30.0:
  version "0.30.0"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

magic-string@^0.30.1:
  version "0.30.2"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

magicast@0.2.8:
  version "0.2.8"
  dependencies:
    "@babel/parser" "^7.21.9"
    "@babel/types" "^7.21.5"
    recast "^0.23.2"

markdown-it@^13.0.1:
  version "13.0.1"
  dependencies:
    argparse "^2.0.1"
    entities "~3.0.1"
    linkify-it "^4.0.1"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

mdurl@^1.0.1:
  version "1.0.1"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"

micromatch@^4.0.4:
  version "4.0.5"
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

micromatch@^4.0.8:
  version "4.0.8"
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime@^3.0.0:
  version "3.0.0"

min-indent@^1.0.0:
  version "1.0.1"

minimalistic-assert@^1.0.1:
  version "1.0.1"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^7.4.1:
  version "7.4.6"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"

minipass@^4.2.4:
  version "4.2.8"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "7.0.1"

minipass@^7.1.2:
  version "7.1.2"

mkdirp@^0.5.1:
  version "0.5.6"
  dependencies:
    minimist "^1.2.6"

mri@^1.1.0:
  version "1.2.0"

mrmime@^1.0.0:
  version "1.0.1"

ms@^2.1.1:
  version "2.1.3"

ms@2.1.2:
  version "2.1.2"

mz@^2.7.0:
  version "2.7.0"
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.4:
  version "3.3.6"

nanoid@^3.3.7:
  version "3.3.8"

natural-compare-lite@^1.4.0:
  version "1.4.0"

natural-compare@^1.4.0:
  version "1.4.0"

node-fetch@^2.6.7:
  version "2.6.12"
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@2.6.7:
  version "2.6.7"
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.8:
  version "2.0.10"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"

normalize-range@^0.1.2:
  version "0.1.2"

numeral@^2.0.6:
  version "2.0.6"

object-assign@^4.0.1:
  version "4.1.1"

object-hash@^1.3.1:
  version "1.3.1"

object-hash@^3.0.0:
  version "3.0.0"

object-inspect@^1.12.3, object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==

object-is@^1.0.1, object-is@^1.1.5:
  version "1.1.5"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.1.1:
  version "1.1.1"

object.assign@^4.1.4:
  version "4.1.4"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.values@^1.1.6:
  version "1.1.6"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

once@^1.3.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

optionator@^0.9.1:
  version "0.9.1"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

p-limit@^2.2.0:
  version "2.3.0"
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"

package-json-from-dist@^1.0.0:
  version "1.0.1"

parchment@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz"
  integrity sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==

parent-module@^1.0.0:
  version "1.0.1"
  dependencies:
    callsites "^3.0.0"

parse-svg-path@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ==

path-exists@^4.0.0:
  version "4.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^3.1.0:
  version "3.1.1"

path-parse@^1.0.7:
  version "1.0.7"

path-scurry@^1.11.1:
  version "1.11.1"
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-scurry@^1.6.1:
  version "1.10.1"
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"

pathval@^1.1.1:
  version "1.1.1"

performance-now@^2.1.0:
  version "2.1.0"

picocolors@^1.0.0:
  version "1.0.0"

picocolors@^1.1.1:
  version "1.1.1"

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"

pify@^2.3.0:
  version "2.3.0"

pirates@^4.0.1:
  version "4.0.5"

pixi.js@*, pixi.js@^6.0.0, pixi.js@^8.10.2:
  version "8.10.2"
  resolved "https://registry.npmjs.org/pixi.js/-/pixi.js-8.10.2.tgz"
  integrity sha512-utRKxzTwNsIhaxOikxIBxKPfxuOyVkPvdYipY23ZEyWSYhONosrQlcB9nymeIcbsrsuSdwExX0eTnHTDjTN3UQ==
  dependencies:
    "@pixi/colord" "^2.9.6"
    "@types/css-font-loading-module" "^0.0.12"
    "@types/earcut" "^3.0.0"
    "@webgpu/types" "^0.1.40"
    "@xmldom/xmldom" "^0.8.10"
    earcut "^3.0.1"
    eventemitter3 "^5.0.1"
    gifuct-js "^2.1.2"
    ismobilejs "^1.1.1"
    parse-svg-path "^0.1.2"

playwright-core@1.36.0:
  version "1.36.0"

pngjs@^5.0.0:
  version "5.0.0"

postcss-import@^15.1.0:
  version "15.1.0"
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"

"postcss@^7 || ^8", postcss@^8.1.0, postcss@^8.4.21:
  version "8.4.21"
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@^8.0.0, postcss@^8.2.14, postcss@^8.4.47, postcss@>=8.0.9:
  version "8.4.49"
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"

prettier-plugin-svelte@^2.8.1:
  version "2.10.0"

"prettier@^1.16.4 || ^2.0.0", prettier@^2.8.0:
  version "2.8.7"

progress@^2.0.3:
  version "2.0.3"

protobufjs@^6.11.3, protobufjs@^6.8.8:
  version "6.11.3"
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" ">=13.7.0"
    long "^4.0.0"

proxy-from-env@^1.1.0:
  version "1.1.0"

punycode@^2.1.0:
  version "2.3.0"

qrcode@^1.5.3:
  version "1.5.3"
  dependencies:
    dijkstrajs "^1.0.1"
    encode-utf8 "^1.0.3"
    pngjs "^5.0.0"
    yargs "^15.3.1"

queue-microtask@^1.2.2:
  version "1.2.3"

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz"
  integrity sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill@1.3.6:
  version "1.3.6"
  resolved "https://registry.npmjs.org/quill/-/quill-1.3.6.tgz"
  integrity sha512-K0mvhimWZN6s+9OQ249CH2IEPZ9JmkFuCQeHAOQax3EZ2nDJ3wfGh59mnlQaZV2i7u8eFarx6wAtvQKgShojug==
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.1"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

raf@^3.4.1:
  version "3.4.1"
  dependencies:
    performance-now "^2.1.0"

read-cache@^1.0.0:
  version "1.0.0"
  dependencies:
    pify "^2.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  dependencies:
    picomatch "^2.2.1"

recast@^0.23.2:
  version "0.23.2"
  dependencies:
    assert "^2.0.0"
    ast-types "^0.16.1"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tslib "^2.0.1"

regenerator-runtime@^0.13.11, regenerator-runtime@^0.13.7:
  version "0.13.11"

regexp.prototype.flags@^1.4.3, regexp.prototype.flags@^1.5.1:
  version "1.5.4"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

require-directory@^2.1.1:
  version "2.1.1"

require-main-filename@^2.0.0:
  version "2.0.0"

resolve-from@^4.0.0:
  version "4.0.0"

resolve@^1.1.7:
  version "1.22.1"
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^1.22.1:
  version "1.22.1"
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^1.22.8:
  version "1.22.8"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"

rgbcolor@^1.0.1:
  version "1.0.1"

rimraf@^2.5.2:
  version "2.7.1"
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.2:
  version "3.0.2"
  dependencies:
    glob "^7.1.3"

rollup@^1.20.0||^2.0.0||^3.0.0, rollup@^2.68.0||^3.0.0, rollup@^2.78.0||^3.0.0, rollup@^3.18.0, rollup@^3.7.0:
  version "3.20.2"
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  dependencies:
    queue-microtask "^1.2.2"

sade@^1.7.4, sade@^1.8.1:
  version "1.8.1"
  dependencies:
    mri "^1.1.0"

safe-regex-test@^1.0.0:
  version "1.0.0"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"

sander@^0.5.0:
  version "0.5.1"
  dependencies:
    es6-promise "^3.1.2"
    graceful-fs "^4.1.3"
    mkdirp "^0.5.1"
    rimraf "^2.5.2"

semver@^6.3.0:
  version "6.3.0"

semver@^7.3.7:
  version "7.3.8"
  dependencies:
    lru-cache "^6.0.0"

set-blocking@^2.0.0:
  version "2.0.0"

set-cookie-parser@^2.6.0:
  version "2.6.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^4.0.1:
  version "4.1.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  dependencies:
    is-arrayish "^0.3.1"

sirv@^2.0.2:
  version "2.0.2"
  dependencies:
    "@polka/url" "^1.0.0-next.20"
    mrmime "^1.0.0"
    totalist "^3.0.0"

slash@^3.0.0:
  version "3.0.0"

socket.io-client@^4.7.5:
  version "4.7.5"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.2"
    engine.io-client "~6.5.2"
    socket.io-parser "~4.2.4"

socket.io-parser@~4.2.4:
  version "4.2.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

sorcery@^0.11.0, sorcery@0.11.0:
  version "0.11.0"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.14"
    buffer-crc32 "^0.2.5"
    minimist "^1.2.0"
    sander "^0.5.0"

source-map-js@^1.0.2:
  version "1.0.2"

source-map-js@^1.2.1:
  version "1.2.1"

source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"

ssf@~0.11.2:
  version "0.11.2"
  dependencies:
    frac "~1.1.2"

stackblur-canvas@^2.0.0:
  version "2.6.0"

streamsearch@^1.1.0:
  version "1.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.trim@^1.2.7:
  version "1.2.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimend@^1.0.6:
  version "1.0.6"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimstart@^1.0.6:
  version "1.0.6"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"

strip-indent@^3.0.0:
  version "3.0.0"
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"

strip-literal@^1.0.0:
  version "1.0.1"
  dependencies:
    acorn "^8.8.2"

sucrase@^3.35.0:
  version "3.35.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^7.1.0:
  version "7.2.0"
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"

svelte-chartjs@^3.1.2:
  version "3.1.2"

svelte-check@^3.0.1:
  version "3.1.4"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.17"
    chokidar "^3.4.1"
    fast-glob "^3.2.7"
    import-fresh "^3.2.1"
    picocolors "^1.0.0"
    sade "^1.7.4"
    svelte-preprocess "^5.0.0"
    typescript "^4.9.4"

svelte-hmr@^0.15.2:
  version "0.15.2"

svelte-imask@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/svelte-imask/-/svelte-imask-1.2.0.tgz"
  integrity sha512-5LNUhokQ3rG3jkjIxewI2VIxoOYdrBCcnLIUddA9qmIO9dhDH+Qs2FkKm7wRsZy3x6WbwOjB4EvEoTMR8KrFqg==
  dependencies:
    imask latest

svelte-pixi@^0.1.3:
  version "0.1.3"

svelte-preprocess@^5.0.0:
  version "5.0.3"
  dependencies:
    "@types/pug" "^2.0.6"
    detect-indent "^6.1.0"
    magic-string "^0.27.0"
    sorcery "^0.11.0"
    strip-indent "^3.0.0"

"svelte@^3.19.0 || ^4.0.0-next.0", svelte@^3.2.0, svelte@^3.23.0, svelte@^3.45.0, svelte@^3.54.0, "svelte@^3.54.0 || ^4.0.0", "svelte@^3.54.0 || ^4.0.0-next.0", svelte@^3.55.0, svelte@^4.2.12, "svelte@3.x || 4.x":
  version "3.58.0"

svg-pathdata@^6.0.3:
  version "6.0.3"

tailwind-scrollbar-hide@^1.1.7:
  version "1.3.1"
  resolved "https://registry.npmjs.org/tailwind-scrollbar-hide/-/tailwind-scrollbar-hide-1.3.1.tgz"
  integrity sha512-eUAvPTltKnAGHbCBRpOk5S7+UZTkFZgDKmZLZ6jZXXs4V7mRXvwshBjeMwrv3vmiWqm3IGEDFVKzUSm1JuoXKw==

tailwindcss@^3.4.13, "tailwindcss@>=3.0.0 || >= 4.0.0 || >= 4.0.0-beta.8 || >= 4.0.0-alpha.20":
  version "3.4.16"
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

text-segmentation@^1.0.3:
  version "1.0.3"
  dependencies:
    utrie "^1.0.2"

text-table@^0.2.0:
  version "0.2.0"

thenify-all@^1.0.0:
  version "1.6.0"
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  dependencies:
    any-promise "^1.0.0"

tinybench@^2.3.1:
  version "2.4.0"

tinypool@^0.3.0:
  version "0.3.1"

tinyspy@^1.0.2:
  version "1.1.1"

to-fast-properties@^2.0.0:
  version "2.0.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

totalist@^3.0.0:
  version "3.0.0"

tr46@~0.0.3:
  version "0.0.3"

ts-interface-checker@^0.1.9:
  version "0.1.13"

ts-poet@^6.4.1:
  version "6.4.1"
  dependencies:
    dprint-node "^1.0.7"

ts-proto-descriptors@1.8.0:
  version "1.8.0"
  dependencies:
    long "^4.0.0"
    protobufjs "^6.8.8"

ts-proto@^1.137.0:
  version "1.146.0"
  dependencies:
    "@types/object-hash" "^1.3.0"
    case-anything "^2.1.10"
    dataloader "^1.4.0"
    object-hash "^1.3.1"
    protobufjs "^6.11.3"
    ts-poet "^6.4.1"
    ts-proto-descriptors "1.8.0"

tsconfig-paths@^3.14.1:
  version "3.14.2"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"

tslib@^2.0.1:
  version "2.6.0"

tslib@^2.1.0, tslib@^2.4.1:
  version "2.5.0"

"tslib@^2.4.1 || ^1.9.3":
  version "2.6.0"

tsutils@^3.21.0:
  version "3.21.0"
  dependencies:
    tslib "^1.8.1"

turndown@^7.1.2:
  version "7.2.0"
  resolved "https://registry.npmjs.org/turndown/-/turndown-7.2.0.tgz"
  integrity sha512-eCZGBN4nNNqM9Owkv9HAtWRYfLA4h909E/WGAWWBpmB275ehNhZyk87/Tpvjbp0jjNl9XwCsbe6bm6CqFsgD+A==
  dependencies:
    "@mixmark-io/domino" "^2.2.0"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  dependencies:
    prelude-ls "^1.2.1"

type-detect@^4.0.0, type-detect@^4.0.5:
  version "4.0.8"

type-fest@^0.20.2:
  version "0.20.2"

typed-array-length@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typescript@^4.9.4, "typescript@>=3.9.5 || ^4.0.0 || ^5.0.0":
  version "4.9.5"

typescript@^5.0.0, "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  version "5.0.3"

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"

unbox-primitive@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici@~5.22.0:
  version "5.22.1"
  dependencies:
    busboy "^1.6.0"

unplugin@1.0.1:
  version "1.0.1"
  dependencies:
    acorn "^8.8.1"
    chokidar "^3.5.3"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.5.0"

update-browserslist-db@^1.0.10:
  version "1.0.10"
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.2:
  version "1.0.2"

util@^0.12.0:
  version "0.12.5"
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utrie@^1.0.2:
  version "1.0.2"
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^9.0.1:
  version "9.0.1"

validator@^13.12.0, validator@^13.9.0:
  version "13.12.0"

"vite@^3.0.0 || ^4.0.0", vite@^4.0.0, vite@^4.2.0:
  version "4.2.1"
  dependencies:
    esbuild "^0.17.5"
    postcss "^8.4.21"
    resolve "^1.22.1"
    rollup "^3.18.0"
  optionalDependencies:
    fsevents "~2.3.2"

vitefu@^0.2.4:
  version "0.2.4"

vitest@^0.25.3:
  version "0.25.8"
  dependencies:
    "@types/chai" "^4.3.4"
    "@types/chai-subset" "^1.3.3"
    "@types/node" "*"
    acorn "^8.8.1"
    acorn-walk "^8.2.0"
    chai "^4.3.7"
    debug "^4.3.4"
    local-pkg "^0.4.2"
    source-map "^0.6.1"
    strip-literal "^1.0.0"
    tinybench "^2.3.1"
    tinypool "^0.3.0"
    tinyspy "^1.0.2"
    vite "^3.0.0 || ^4.0.0"

web-vitals@^3.5.1:
  version "3.5.2"

webidl-conversions@^3.0.0:
  version "3.0.1"

webpack-sources@^3.2.3, webpack-sources@3.2.3:
  version "3.2.3"

webpack-virtual-modules@^0.5.0:
  version "0.5.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.1"

which-typed-array@^1.1.2, which-typed-array@^1.1.9:
  version "1.1.9"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

wmf@~1.0.1:
  version "1.0.2"

word-wrap@^1.2.3:
  version "1.2.3"

word@~0.3.0:
  version "0.3.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"

ws@~8.17.1:
  version "8.17.1"

xlsx@^0.18.5:
  version "0.18.5"
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xmlhttprequest-ssl@~2.0.0:
  version "2.0.0"

y18n@^4.0.0:
  version "4.0.3"

yallist@^4.0.0:
  version "4.0.0"

yaml@^2.3.4:
  version "2.6.1"

yargs-parser@^18.1.2:
  version "18.1.3"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^15.3.1:
  version "15.4.1"
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yocto-queue@^0.1.0:
  version "0.1.0"
